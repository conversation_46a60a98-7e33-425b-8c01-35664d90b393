// PromptCircle API integration service
// Based on the demo's background.js API functionality

export interface OptimizationSuggestion {
  label: string;
  content: string;
}

export interface OptimizationResponse {
  suggestions: OptimizationSuggestion[];
}

export interface CommunityPrompt {
  id: number;
  title: string;
  content: string;
  description: string;
  tags: string;
  category: string;
  compatible_llms: string;
  example_use_case: string;
  upvotes: number;
  downvotes: number;
  rating_score: number;
  usage_count: number;
  is_featured: boolean;
  is_verified: boolean;
  user: string;
  created_at: string;
}

export interface CommunityPromptsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  prompts: CommunityPrompt[];
}

export interface ApiError {
  error: string;
}

export class PromptCircleApiService {
  private baseURL = "https://www.promptcircle.ai";
  private apiURL = "https://www.promptcircle.ai/api";

  /**
   * Check if user is authenticated with PromptCircle
   */
  async checkAuth(): Promise<{ isAuthenticated: boolean }> {
    try {
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const sessionCookie = cookies.find((c) => c.name === "sessionid");

      return { isAuthenticated: !!sessionCookie };
    } catch (error) {
      console.error("Error checking auth:", error);
      return { isAuthenticated: false };
    }
  }

  /**
   * Optimize a prompt using PromptCircle API
   */
  async optimizePrompt(
    prompt: string
  ): Promise<OptimizationResponse | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(`${this.apiURL}/optimize-prompt/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfCookie.value,
        },
        body: JSON.stringify({ prompt }),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      return data as OptimizationResponse;
    } catch (error) {
      console.error("Error optimizing prompt:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Open PromptCircle login page
   */
  openLoginPage(): void {
    chrome.tabs.create({
      url: "https://www.promptcircle.ai/accounts/login/",
    });
  }

  /**
   * Logout from PromptCircle (clear cookies)
   */
  async logout(): Promise<void> {
    try {
      await chrome.cookies.remove({
        url: "https://www.promptcircle.ai",
        name: "sessionid",
      });

      await chrome.cookies.remove({
        url: "https://www.promptcircle.ai",
        name: "csrftoken",
      });
    } catch (error) {
      console.error("Error logging out:", error);
    }
  }

  /**
   * Get user session info from PromptCircle
   * Try multiple endpoints to get user information
   */
  async getSessionInfo(): Promise<any> {
    try {
      // First try the allauth session endpoint
      let response = await fetch(
        `${this.baseURL}/_allauth/browser/v1/auth/session/`,
        {
          credentials: "include",
        }
      );

      if (response.ok) {
        return await response.json();
      }

      // If allauth endpoint fails, try a user profile endpoint
      response = await fetch(`${this.apiURL}/user/profile/`, {
        credentials: "include",
      });

      if (response.ok) {
        return await response.json();
      }

      // If both fail, try to get basic info from cookies and create a minimal user object
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });

      const sessionCookie = cookies.find((c) => c.name === "sessionid");
      if (sessionCookie) {
        // Return a minimal user object based on authentication status
        return {
          user: {
            email: "<EMAIL>", // Placeholder
            username: "PromptCircle User",
            first_name: "",
            last_name: "",
          },
          isAuthenticated: true,
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting session info:", error);
      return null;
    }
  }

  /**
   * Get community prompts with pagination
   */
  async getCommunityPrompts(
    page: number = 1
  ): Promise<CommunityPromptsResponse | ApiError> {
    try {
      // Check authentication first
      const authCheck = await this.checkAuth();
      if (!authCheck.isAuthenticated) {
        return { error: "User not authenticated with PromptCircle" };
      }

      // Get CSRF token from cookies
      const cookies = await chrome.cookies.getAll({
        domain: "promptcircle.ai",
      });
      const csrfCookie = cookies.find((c) => c.name === "csrftoken");

      if (!csrfCookie) {
        return {
          error: "No CSRF token found. Please login to PromptCircle first.",
        };
      }

      // Make API request
      const response = await fetch(
        `${this.apiURL}/community-prompts/?page=${page}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": csrfCookie.value,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      console.log("--------data", data);
      return data as CommunityPromptsResponse;
    } catch (error) {
      console.error("Error fetching community prompts:", error);
      return {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}

// Export singleton instance
export const promptCircleApi = new PromptCircleApiService();
