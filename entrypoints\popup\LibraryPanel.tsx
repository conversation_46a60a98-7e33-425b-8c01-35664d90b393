import React, { useState, useEffect } from 'react';
import { <PERSON>ton, <PERSON>po<PERSON>, <PERSON>, Snackbar, Modal, Chip, IconButton, Card, CardContent, Tooltip, TextField, Select, MenuItem, InputLabel, FormControl, Divider } from '@mui/material';
import PromptSaveModal, { PromptData } from './PromptSaveModal';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SendIcon from '@mui/icons-material/Send';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import LabelIcon from '@mui/icons-material/Label';
import FolderIcon from '@mui/icons-material/Folder';
import CloseIcon from '@mui/icons-material/Close';
import CopyIcon from '@mui/icons-material/ContentCopy';
import SmartToyIcon from '@mui/icons-material/SmartToy';

export default function LibraryPanel({ onUsePrompt }: { onUsePrompt: (prompt: string) => void }) {
  const [library, setLibrary] = useState<{
    id: string;
    title: string;
    content: string;
    tags: string[];
    folder: string;
    isFavorite: boolean;
    createdAt: number;
    updatedAt: number;
  }[]>([]);
  const [search, setSearch] = useState('');
  const [newTitle, setNewTitle] = useState('');
  const [newContent, setNewContent] = useState('');
  const [newTags, setNewTags] = useState<string[]>([]);
  const [newFolder, setNewFolder] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [sortBy, setSortBy] = useState<'createdAt' | 'updatedAt' | 'title' | 'favorite'>('createdAt');
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add'|'edit'>('add');
  const [modalInitial, setModalInitial] = useState<Partial<PromptData>>({});
  const [selectedLLM, setSelectedLLM] = useState<'ChatGPT' | 'Claude' | 'Gemini'>('ChatGPT');

  useEffect(() => {
    chrome.storage.local.get(['promptLibrary'], (result) => {
      if (Array.isArray(result.promptLibrary)) {
        setLibrary(result.promptLibrary);
      }
    });
  }, []);

  const saveLibrary = async (lib: typeof library) => {
    setLibrary(lib);
    await chrome.storage.local.set({ promptLibrary: lib });
  };

  // Open modal for add or edit
  const openAddModal = () => {
    setModalInitial({});
    setModalMode('add');
    setModalOpen(true);
  };
  const openEditModal = (item: typeof library[0]) => {
    setModalInitial({
      title: item.title,
      content: item.content,
      tags: item.tags,
      folder: item.folder,
      optimize: ''
    });
    setEditingId(item.id);
    setModalMode('edit');
    setModalOpen(true);
  };
  const closeModal = () => {
    setModalOpen(false);
    setEditingId(null);
  };

  // Tag input helpers
  const [tagInput, setTagInput] = useState('');
  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !newTags.includes(tag)) {
      setNewTags([...newTags, tag]);
    }
    setTagInput('');
  };
  const handleRemoveTag = (tag: string) => {
    setNewTags(newTags.filter(t => t !== tag));
  };

  // Folder input helpers
  const [folderInput, setFolderInput] = useState('');
  const handleSelectFolder = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setNewFolder(e.target.value);
    setFolderInput('');
  };
  const handleFolderInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFolderInput(e.target.value);
    setNewFolder(e.target.value);
  };

  const handleSaveModal = async (data: PromptData) => {
    const now = Date.now();
    let updated;
    if (modalMode === 'edit' && editingId) {
      updated = library.map(item =>
        item.id === editingId
          ? {
              ...item,
              ...data,
              updatedAt: now
            }
          : item
      );
      setSnackbar({ open: true, message: 'Prompt updated!', severity: 'success' });
    } else {
      updated = [
        {
          id: Date.now().toString(),
          ...data,
          isFavorite: false,
          createdAt: now,
          updatedAt: now
        },
        ...library
      ];
      setSnackbar({ open: true, message: 'Prompt added!', severity: 'success' });
    }
    await saveLibrary(updated);
    setModalOpen(false);
    setEditingId(null);
  };

  // Extract unique folders and tags for filter dropdowns
  const allFolders = Array.from(new Set(library.map(item => item.folder).filter(Boolean)));
  const allTags = Array.from(new Set(library.flatMap(item => item.tags || [])));

  // Filtered and sorted prompts
  const filtered = library
    .filter(item => {
      const matchesSearch =
        item.title.toLowerCase().includes(search.toLowerCase()) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase())));
      const matchesFolder = selectedFolder ? item.folder === selectedFolder : true;
      const matchesTag = selectedTag ? (item.tags && item.tags.includes(selectedTag)) : true;
      return matchesSearch && matchesFolder && matchesTag;
    })
    .sort((a, b) => {
      if (sortBy === 'title') {
        return a.title.localeCompare(b.title);
      } else if (sortBy === 'favorite') {
        return b.isFavorite ? 1 : -1;
      } else {
        return b[sortBy] - a[sortBy];
      }
    });

  // Handler to toggle favorite
  const handleToggleFavorite = async (id: string) => {
    const updated = library.map(item =>
      item.id === id ? { ...item, isFavorite: !item.isFavorite, updatedAt: Date.now() } : item
    );
    await saveLibrary(updated);
  };

  // Handler to delete a prompt
  const handleDelete = async (id: string) => {
    const updated = library.filter(item => item.id !== id);
    await saveLibrary(updated);
    setSnackbar({ open: true, message: 'Prompt deleted.', severity: 'success' });
  };

  // Handler to copy prompt content
  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbar({ open: true, message: 'Prompt copied to clipboard!', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to copy prompt.', severity: 'error' });
    }
  };

  // Handler to send prompt to LLM (same logic as PromptPanel)
  const handleSend = async (promptContent: string) => {
    if (!promptContent.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }

    let url = '';
    let selector = '';

    switch (selectedLLM) {
      case 'ChatGPT':
        url = 'https://chat.openai.com/';
        selector = 'textarea[data-id="root"], textarea[placeholder*="Message"], textarea[placeholder*="Send a message"]';
        break;
      case 'Gemini':
        url = 'https://gemini.google.com/app';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Claude':
        url = 'https://claude.ai/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      default:
        setSnackbar({ open: true, message: 'Invalid LLM selection.', severity: 'error' });
        return;
    }

    setSnackbar({ open: true, message: `Opening ${selectedLLM}...`, severity: 'success' });

    // Send message to background to handle tab, injection, handshake, and fillPrompt
    chrome.runtime.sendMessage({
      action: 'openAndFillPrompt',
      url,
      prompt: promptContent,
      selector
    }, (response) => {
      if (response && response.success) {
        setSnackbar({ open: true, message: `Prompt filled in ${selectedLLM}!`, severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response && response.error ? response.error : 'Failed to fill prompt.', severity: 'error' });
      }
    });
  };

  return (
    <>
      {/* Header: Search & Filters, styled like CommunityPanel */}
      <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
        <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>🔍 Search & Filters</Typography>
        <TextField
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder="Search prompts by keyword..."
          size="small"
          fullWidth
          InputProps={{
            startAdornment: <span role="img" aria-label="search">🔎</span>,
            style: { borderRadius: 8 }
          }}
          sx={{ mb: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {/* Tag Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={e => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {allTags.map(tag => (
                <MenuItem key={tag} value={tag}>{tag}</MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* LLM Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🤖 LLM</InputLabel>
            <Select
              value={selectedLLM}
              label="🤖 LLM"
              onChange={e => setSelectedLLM(e.target.value as 'ChatGPT' | 'Claude' | 'Gemini')}
            >
              <MenuItem value="ChatGPT">ChatGPT</MenuItem>
              <MenuItem value="Claude">Claude</MenuItem>
              <MenuItem value="Gemini">Gemini</MenuItem>
            </Select>
          </FormControl>
          {/* Sort Filter */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>📅 Sort</InputLabel>
            <Select
              value={sortBy}
              label="📅 Sort"
              onChange={e => setSortBy(e.target.value as any)}
            >
              <MenuItem value="createdAt">Newest</MenuItem>
              <MenuItem value="updatedAt">Recently Updated</MenuItem>
              <MenuItem value="title">Title</MenuItem>
              <MenuItem value="favorite">Favorite</MenuItem>
            </Select>
          </FormControl>
          {/* Folder Filter (optional, keep if you want) */}
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🗂️ Folder</InputLabel>
            <Select
              value={selectedFolder}
              label="🗂️ Folder"
              onChange={e => setSelectedFolder(e.target.value)}
            >
              <MenuItem value="">All Folders</MenuItem>
              {allFolders.map(folder => (
                <MenuItem key={folder} value={folder}>{folder}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
      {/* Add New Prompt Button (remains below header) */}
      <Box sx={{ p: 2, pt: 1, background: '#fafafa' }}>
        <Button variant="contained" color="primary" onClick={openAddModal} sx={{ alignSelf: 'flex-end', borderRadius: 2, fontWeight: 600, fontSize: 12 }}>+ ADD NEW PROMPT</Button>
      </Box>
      {/* Prompt List and Modal remain unchanged */}
      <PromptSaveModal
        open={modalOpen}
        onClose={closeModal}
        onSave={handleSaveModal}
        initialPrompt={modalInitial}
        allFolders={allFolders}
        mode={modalMode}
      />
      {/* Prompt List */}
      <Box sx={{ flex: 1, overflowY: 'auto', background: '#f9fafb', p: 2, display: 'flex', flexDirection: 'column', gap: 2, minHeight: 0 }}>
        {filtered.length === 0 ? (
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>No prompts found.</Typography>
        ) : (
          filtered.map(item => (
            <Card key={item.id} sx={{ borderRadius: 2, border: '1px solid #e0e0e0', boxShadow: 0, p: 0, position: 'relative', overflow: 'visible' }}>
              {item.isFavorite && (
                <Box sx={{ position: 'absolute', top: 8, right: 8, bgcolor: '#ffd700', color: '#b8860b', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                  <StarIcon fontSize="inherit" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} /> FAVORITE
                </Box>
              )}
              <CardContent sx={{ p: 2, pb: 1 }}>
                {/* Header Section */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <span role="img" aria-label="prompt" style={{ fontSize: 20 }}>📝</span>
                  <Typography fontWeight={700} fontSize={15} sx={{ flex: 1 }}>{item.title}</Typography>
                </Box>
                {/* Content Preview */}
                <Typography fontSize={13} color="text.secondary" sx={{ mb: 1, whiteSpace: 'pre-line' }}>{item.content.length > 120 ? item.content.slice(0, 120) + '...' : item.content}</Typography>
                {/* Tags and LLM */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 1 }}>
                  {item.tags && item.tags.map(tag => (
                    <Chip key={tag} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                  ))}
                  <Chip icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={selectedLLM} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
                </Box>
                {/* Metadata Row */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                  <span>Created: {new Date(item.createdAt).toLocaleDateString()}</span>
                  {item.updatedAt !== item.createdAt && (
                    <span>Updated: {new Date(item.updatedAt).toLocaleDateString()}</span>
                  )}
                  {item.isFavorite && <span>⭐ Favorite</span>}
                </Box>
                <Divider sx={{ my: 1 }} />
                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
                  <Tooltip title={`Send to ${selectedLLM}`}><IconButton color="primary" size="small" onClick={() => handleSend(item.content)}><SendIcon /></IconButton></Tooltip>
                  <Tooltip title="Copy to clipboard"><IconButton size="small" onClick={() => handleCopyContent(item.content)}><CopyIcon /></IconButton></Tooltip>
                  <Tooltip title="Edit prompt"><IconButton size="small" onClick={() => openEditModal(item)}><EditIcon /></IconButton></Tooltip>
                  <Tooltip title={item.isFavorite ? 'Remove from favorites' : 'Add to favorites'}><IconButton size="small" onClick={() => handleToggleFavorite(item.id)}>{item.isFavorite ? <StarIcon fontSize="small" /> : <StarBorderIcon fontSize="small" />}</IconButton></Tooltip>
                  <Tooltip title="Delete prompt"><IconButton size="small" color="error" onClick={() => handleDelete(item.id)}><DeleteIcon /></IconButton></Tooltip>
                </Box>
              </CardContent>
            </Card>
          ))
        )}
      </Box>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        ContentProps={{ style: { background: snackbar.severity === 'error' ? '#d32f2f' : '#388e3c', color: '#fff' } }}
        action={
          <Button
            size="small"
            onClick={() => setSnackbar(s => ({ ...s, open: false }))}
            sx={{ color: '#ffffff', minWidth: 'auto' }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        }
      />
    </>
  );
} 