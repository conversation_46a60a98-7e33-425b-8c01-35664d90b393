import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Chip,
  Card,
  CardContent,
  IconButton,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Modal,
  Snackbar
} from '@mui/material';
import {
  Search as SearchIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  ContentCopy as CopyIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { getBookmarks, removeBookmark, getAllTags, searchBookmarks, getBookmarksByTag } from '../../utils/bookmark';
import type { Bookmark } from '../../utils/bookmark';

interface SavedResponsesProps {
  onClose?: () => void;
}

export default function SavedResponses({ onClose }: SavedResponsesProps) {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [filteredBookmarks, setFilteredBookmarks] = useState<Bookmark[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [allTags, setAllTags] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedBookmark, setSelectedBookmark] = useState<Bookmark | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load bookmarks on component mount
  useEffect(() => {
    loadBookmarks();
  }, []);

  // Filter bookmarks when search, tag, or source changes
  useEffect(() => {
    filterBookmarks();
  }, [bookmarks, searchQuery, selectedTag, selectedSource]);

  const loadBookmarks = async () => {
    try {
      setLoading(true);
      const [bookmarksData, tagsData] = await Promise.all([
        getBookmarks(),
        getAllTags()
      ]);
      setBookmarks(bookmarksData);
      setAllTags(tagsData);
    } catch (err) {
      setError('Failed to load bookmarks');
      console.error('Error loading bookmarks:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterBookmarks = async () => {
    let filtered = [...bookmarks];

    // Apply search filter
    if (searchQuery.trim()) {
      try {
        const searchResults = await searchBookmarks(searchQuery);
        filtered = filtered.filter(bookmark => 
          searchResults.some(result => result.id === bookmark.id)
        );
      } catch (err) {
        console.error('Error searching bookmarks:', err);
      }
    }

    // Apply tag filter
    if (selectedTag) {
      filtered = filtered.filter(bookmark => 
        bookmark.tags.includes(selectedTag)
      );
    }

    // Apply source filter
    if (selectedSource) {
      filtered = filtered.filter(bookmark => 
        bookmark.source === selectedSource
      );
    }

    setFilteredBookmarks(filtered);
  };

  const handleDeleteBookmark = async (bookmarkId: string) => {
    try {
      await removeBookmark(bookmarkId);
      setBookmarks(prev => prev.filter(b => b.id !== bookmarkId));
      setSnackbar({ open: true, message: 'Bookmark deleted successfully', severity: 'success' });
    } catch (err) {
      setError('Failed to delete bookmark');
      console.error('Error deleting bookmark:', err);
    }
  };

  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbar({ open: true, message: 'Content copied to clipboard!', severity: 'success' });
    } catch (err) {
      console.error('Error copying content:', err);
      setSnackbar({ open: true, message: 'Failed to copy content', severity: 'error' });
    }
  };

  const handleViewBookmark = (bookmark: Bookmark) => {
    setSelectedBookmark(bookmark);
    setViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setViewModalOpen(false);
    setSelectedBookmark(null);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTag('');
    setSelectedSource('');
  };

  const getUniqueSources = () => {
    return [...new Set(bookmarks.map(b => b.source))].sort();
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      {/* Header: Search & Filters, styled like Community/LibraryPanel */}
      <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
        <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>🔍 Search & Filters</Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
          {/* Search Bar */}
          <TextField
            fullWidth
            size="small"
            placeholder="Search bookmarks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <span role="img" aria-label="search">🔎</span>,
              endAdornment: searchQuery && (
                <IconButton size="small" onClick={() => setSearchQuery('')}>
                  <ClearIcon />
                </IconButton>
              ),
              style: { borderRadius: 8 }
            }}
            sx={{ mb: 0, flex: 2, minWidth: 180 }}
          />
          {/* Tag Filter */}
          <FormControl size="small" sx={{ minWidth: 120, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={(e) => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {allTags.map(tag => (
                <MenuItem key={tag} value={tag}>
                  #{tag}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Source Filter */}
          <FormControl size="small" sx={{ minWidth: 120, flex: 1 }}>
            <InputLabel>📋 Source</InputLabel>
            <Select
              value={selectedSource}
              label="📋 Source"
              onChange={(e) => setSelectedSource(e.target.value)}
            >
              <MenuItem value="">All Sources</MenuItem>
              {getUniqueSources().map(source => (
                <MenuItem key={source} value={source}>
                  {source}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Clear Button */}
          <Button
            variant="outlined"
            size="small"
            startIcon={<FilterIcon />}
            onClick={clearFilters}
            sx={{ minWidth: 80 }}
          >
            Clear
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Bookmarks List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {filteredBookmarks.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography color="text.secondary">
              {bookmarks.length === 0 
                ? "No saved responses yet. Bookmark responses from ChatGPT, Gemini, or Claude to see them here."
                : "No bookmarks match your search criteria."
              }
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {filteredBookmarks.map((bookmark) => (
              <Card key={bookmark.id} sx={{ 
                border: '1px solid #e0e0e0',
                '&:hover': { borderColor: '#4a90e2', boxShadow: 2 }
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle1" fontWeight={600} sx={{ color: '#1e293b' }}>
                      {bookmark.title || 'Untitled'}
                    </Typography>
                    <Box>
                      <Tooltip title="Copy content">
                        <IconButton 
                          size="small" 
                          onClick={() => handleCopyContent(bookmark.content)}
                        >
                          <CopyIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="View full content">
                        <IconButton 
                          size="small"
                          onClick={() => handleViewBookmark(bookmark)}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete bookmark">
                        <IconButton 
                          size="small" 
                          onClick={() => handleDeleteBookmark(bookmark.id)}
                          sx={{ color: 'error.main' }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <Typography variant="body2" sx={{ mt: 1, mb: 1, color: '#374151' }}>
                    {bookmark.content.length > 200 
                      ? `${bookmark.content.substring(0, 200)}...` 
                      : bookmark.content
                    }
                  </Typography>

                  {/* Tags */}
                  {bookmark.tags.length > 0 && (
                    <Box sx={{ mb: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {bookmark.tags.map(tag => (
                        <Chip
                          key={tag}
                          label={`#${tag}`}
                          size="small"
                          sx={{ background: '#e0e0e0', color: '#374151' }}
                        />
                      ))}
                    </Box>
                  )}

                  {/* Note */}
                  {bookmark.note && (
                    <Typography 
                      variant="caption" 
                      sx={{ color: '#64748b', fontStyle: 'italic' }}
                    >
                      {bookmark.note}
                    </Typography>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {bookmark.source} • {formatDate(bookmark.timestamp)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        )}
      </Box>

      {/* View Modal */}
      <Modal open={viewModalOpen} onClose={handleCloseViewModal}>
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '90%',
          maxWidth: 800,
          maxHeight: '90vh',
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 3,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {selectedBookmark && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5" fontWeight={600} sx={{ color: '#1e293b' }}>
                  {selectedBookmark.title || 'Untitled'}
                </Typography>
                <Box>
                  <Tooltip title="Copy content">
                    <IconButton 
                      size="small" 
                      onClick={() => handleCopyContent(selectedBookmark.content)}
                    >
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <IconButton size="small" onClick={handleCloseViewModal}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>

              {/* Tags */}
              {selectedBookmark.tags.length > 0 && (
                <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selectedBookmark.tags.map(tag => (
                    <Chip
                      key={tag}
                      label={`#${tag}`}
                      size="small"
                      sx={{ background: '#e0e0e0', color: '#374151' }}
                    />
                  ))}
                </Box>
              )}

              {/* Note */}
              {selectedBookmark.note && (
                <Box sx={{ mb: 2, p: 2, bgcolor: '#f8f9fa', borderRadius: 1, borderLeft: '4px solid #4a90e2' }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontStyle: 'italic' }}>
                    <strong>Note:</strong> {selectedBookmark.note}
                  </Typography>
                </Box>
              )}

              {/* Content */}
              <Box sx={{ 
                flex: 1, 
                overflow: 'auto', 
                bgcolor: '#f8f9fa', 
                p: 2, 
                borderRadius: 1,
                border: '1px solid #e0e0e0'
              }}>
                <Typography variant="body1" sx={{ 
                  color: '#374151', 
                  lineHeight: 1.6,
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'monospace'
                }}>
                  {selectedBookmark.content}
                </Typography>
              </Box>

              {/* Metadata */}
              <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #e0e0e0' }}>
                <Typography variant="caption" color="text.secondary">
                  {selectedBookmark.source} • {formatDate(selectedBookmark.timestamp)}
                </Typography>
              </Box>
            </>
          )}
        </Box>
      </Modal>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
} 