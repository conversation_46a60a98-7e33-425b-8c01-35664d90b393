import { nanoid } from 'nanoid';
import { API_KEYS } from './config';

export interface Bookmark {
  id: string;
  title: string;
  content: string;
  nodeSelector: string;  // Selector to find the exact message node
  timestamp: number;
  tags: string[];
  note?: string;
  source: string; // e.g., "ChatGPT", "Gemini", "Claude"
  url: string; // page URL when bookmarked
}

// Generate a title from content (first sentence or first 50 characters)
export function generateTitleFromContent(content: string): string {
  // Try to extract first sentence
  const firstSentence = content.match(/^[^.!?]+[.!?]/);
  if (firstSentence) {
    return firstSentence[0].trim();
  }
  
  // Fallback to first 50 characters
  return content.substring(0, 50).trim() + (content.length > 50 ? '...' : '');
}

// AI-powered tag suggestions based on content
export async function generateTagSuggestions(content: string): Promise<string[]> {
  // Check if API key is available
  if (!API_KEYS.GEMINI_API_KEY) {
    console.log('No Gemini API key available, using fallback tag suggestions');
    return getFallbackTagSuggestions(content);
  }

  try {
    // Use the existing perfectPrompt infrastructure to generate tag suggestions
    const prompt = `Analyze the following AI response and suggest 3-5 relevant tags (single words or short phrases) that would help categorize this content for future reference. Focus on the main topics, skills, or use cases mentioned.

Response: ${content.substring(0, 1000)} // Limit content length

Return only the tags as a comma-separated list, no explanations. Example: coding, javascript, tips, debugging`;

    // Try Gemini first (usually faster for this type of task)
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEYS.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 100,
        }
      })
    });

    if (response.ok) {
      const data = await response.json();
      const suggestions = data.candidates?.[0]?.content?.parts?.[0]?.text;
      if (suggestions) {
        return suggestions
          .split(',')
          .map((tag: string) => tag.trim().toLowerCase())
          .filter((tag: string) => tag.length > 0 && tag.length < 20);
      }
    }
  } catch (error) {
    console.log('AI tag suggestions failed, using fallback:', error);
  }

  return getFallbackTagSuggestions(content);
}

// Fallback tag suggestions using basic keyword extraction
function getFallbackTagSuggestions(content: string): string[] {
  const commonTags = [
    'coding', 'javascript', 'python', 'react', 'node', 'api', 'database', 'frontend', 'backend',
    'design', 'ui', 'ux', 'marketing', 'seo', 'content', 'writing', 'analysis', 'data',
    'tips', 'tutorial', 'debug', 'optimization', 'performance', 'security', 'testing',
    'deployment', 'docker', 'aws', 'cloud', 'mobile', 'web', 'app', 'tool', 'framework'
  ];

  const contentLower = content.toLowerCase();
  const matchedTags = commonTags.filter(tag => contentLower.includes(tag));
  
  // Add some generic tags based on content length and type
  if (content.length > 500) matchedTags.push('detailed');
  if (content.includes('code') || content.includes('function') || content.includes('class')) matchedTags.push('code');
  if (content.includes('error') || content.includes('bug') || content.includes('fix')) matchedTags.push('debug');
  
  return [...new Set(matchedTags)].slice(0, 5);
}

// Get all bookmarks
export async function getBookmarks(): Promise<Bookmark[]> {
  const result = await chrome.storage.local.get('bookmarks');
  return result.bookmarks || [];
}

// Add bookmark with node information
export async function addBookmark(content: string, node: Element): Promise<void> {
  const bookmarks = await getBookmarks();
  
  // Generate a unique data attribute to identify this node
  const messageId = `message-${nanoid()}`;
  node.setAttribute('data-message-id', messageId);
  
  const newBookmark: Bookmark = {
    id: nanoid(),
    title: generateTitleFromContent(content),
    content,
    nodeSelector: `[data-message-id="${messageId}"]`,
    timestamp: Date.now(),
    tags: [],
    source: getCurrentSource(),
    url: window.location.href
  };

  bookmarks.push(newBookmark);
  await chrome.storage.local.set({ bookmarks });
}

// Add bookmark with enhanced metadata
export async function addBookmarkWithMetadata(
  content: string, 
  node: Element, 
  title?: string,
  tags: string[] = [], 
  note?: string
): Promise<void> {
  const bookmarks = await getBookmarks();
  
  // Generate a unique data attribute to identify this node
  const messageId = `message-${nanoid()}`;
  node.setAttribute('data-message-id', messageId);
  
  const newBookmark: Bookmark = {
    id: nanoid(),
    title: title || generateTitleFromContent(content),
    content,
    nodeSelector: `[data-message-id="${messageId}"]`,
    timestamp: Date.now(),
    tags,
    note,
    source: getCurrentSource(),
    url: window.location.href
  };

  bookmarks.push(newBookmark);
  await chrome.storage.local.set({ bookmarks });
}

// Update bookmark metadata
export async function updateBookmarkMetadata(
  id: string, 
  title?: string,
  tags?: string[], 
  note?: string
): Promise<void> {
  const bookmarks = await getBookmarks();
  const bookmarkIndex = bookmarks.findIndex(b => b.id === id);
  
  if (bookmarkIndex !== -1) {
    if (title !== undefined) {
      bookmarks[bookmarkIndex].title = title;
    }
    if (tags !== undefined) {
      bookmarks[bookmarkIndex].tags = tags;
    }
    if (note !== undefined) {
      bookmarks[bookmarkIndex].note = note;
    }
    await chrome.storage.local.set({ bookmarks });
  }
}

// Get bookmarks by tag
export async function getBookmarksByTag(tag: string): Promise<Bookmark[]> {
  const bookmarks = await getBookmarks();
  return bookmarks.filter(b => b.tags.includes(tag));
}

// Get all unique tags
export async function getAllTags(): Promise<string[]> {
  const bookmarks = await getBookmarks();
  const allTags = bookmarks.flatMap(b => b.tags);
  return [...new Set(allTags)].sort();
}

// Search bookmarks
export async function searchBookmarks(query: string): Promise<Bookmark[]> {
  const bookmarks = await getBookmarks();
  const lowerQuery = query.toLowerCase();
  
  return bookmarks.filter(b => 
    b.title.toLowerCase().includes(lowerQuery) ||
    b.content.toLowerCase().includes(lowerQuery) ||
    b.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
    (b.note && b.note.toLowerCase().includes(lowerQuery))
  );
}

// Get current source based on URL
function getCurrentSource(): string {
  const url = window.location.href;
  if (url.includes('chatgpt.com')) return 'ChatGPT';
  if (url.includes('gemini.google.com')) return 'Gemini';
  if (url.includes('claude.ai')) return 'Claude';
  if (url.includes('perplexity.ai')) return 'Perplexity';
  if (url.includes('aistudio.google.com')) return 'Google AI Studio';
  if (url.includes('copilot.microsoft.com')) return 'Copilot';
  if (url.includes('grok.com')) return 'Grok';
  return 'Unknown';
}

// Remove bookmark and its node attribute
export async function removeBookmark(id: string): Promise<void> {
  const bookmarks = await getBookmarks();
  const bookmarkToRemove = bookmarks.find(b => b.id === id);
  
  if (bookmarkToRemove) {
    // Remove the data attribute from the node if it exists
    const node = document.querySelector(bookmarkToRemove.nodeSelector);
    if (node) {
      node.removeAttribute('data-message-id');
    }
  }
  
  const updatedBookmarks = bookmarks.filter(b => b.id !== id);
  await chrome.storage.local.set({ bookmarks: updatedBookmarks });
}

// Check if content is bookmarked
export async function isBookmarked(content: string): Promise<boolean> {
  const bookmarks = await getBookmarks();
  return bookmarks.some(b => b.content === content);
}

// Find and scroll to bookmarked message
export async function scrollToBookmark(bookmark: Bookmark): Promise<void> {
  const node = document.querySelector(bookmark.nodeSelector);
  if (!node) {
    console.log("Bookmarked message not found");
    return;
  }

  // Scroll the node into view with smooth animation
  node.scrollIntoView({
    behavior: 'smooth',
    block: 'center'
  });

  // Add highlight effect
  const element = node as HTMLElement;
  element.style.transition = 'all 0.3s ease';
  element.style.backgroundColor = 'rgba(74, 144, 226, 0.1)';
  element.style.borderRadius = '8px';
  element.style.boxShadow = '0 0 0 2px rgba(74, 144, 226, 0.3)';

  // Remove highlight after animation
  setTimeout(() => {
    element.style.backgroundColor = '';
    element.style.boxShadow = '';
    element.style.borderRadius = '';
    element.style.transition = '';
  }, 2000);
} 