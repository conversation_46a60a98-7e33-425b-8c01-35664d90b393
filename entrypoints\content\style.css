.prompt-perfect-buttons-container {
  display: flex;
  gap: 8px;
  margin: 8px 0;
  padding: 4px;
}

.perfect-button,
.feedback-button,
.save-prompt-button {
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.perfect-button {
  background-color: #4285F4;
  color: white;
}

.perfect-button:hover {
  background-color: #3367D6;
}

.feedback-button {
  background-color: #34A853;
  color: white;
}

.feedback-button:hover {
  background-color: #2D8E4A;
}

.save-prompt-button {
  background-color: #FBBC05;
  color: white;
}

.save-prompt-button:hover {
  background-color: #F29900;
}

/* Disabled state */
.perfect-button:disabled,
.feedback-button:disabled,
.save-prompt-button:disabled {
  background-color: #E0E0E0;
  color: #9E9E9E;
  cursor: not-allowed;
}

/* Loading state */
.perfect-button.loading,
.feedback-button.loading,
.save-prompt-button.loading {
  position: relative;
  color: transparent;
}

.perfect-button.loading::after,
.feedback-button.loading::after,
.save-prompt-button.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Tooltip styles */
[title] {
  position: relative;
}

[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 8px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 4px;
} 