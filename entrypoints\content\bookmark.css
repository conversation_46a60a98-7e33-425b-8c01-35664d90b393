.table-content-container {
  position: fixed;
  right: 20px;
  top: 20px;
  bottom: 20px;
  width: 300px;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  padding: 20px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  border-radius: 20px;
  border: 1px solid #eaeaea;
  opacity: 1;
  transform: translateX(0) scale(1);
  will-change: transform, opacity;
  visibility: visible;
  display: flex;
  flex-direction: column;
}

.table-content-container.hidden {
  opacity: 0;
  transform: translateX(40px) scale(0.95);
  pointer-events: none;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1), visibility 0s 0.4s;
}

.table-content-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
  transform: translateY(0);
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s;
}

.table-content-container.hidden .table-content-header {
  transform: translateY(-10px);
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-content-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-content-title::before {
  content: '';
  font-size: 20px;
  /* Add direct background-image as a fallback if content doesn't work */
  display: inline-block;
  width: 24px;
  height: 24px;
  background-image: var(--extension-icon-url);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.table-content-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
  color: #666;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, color;
}

.table-content-close:hover {
  color: #1a1a1a;
  transform: scale(1.1) rotate(90deg);
}

.table-content-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
  margin-right: -10px;
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transform: translateY(0);
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  padding: 0;
  background: none;
  gap: 0;
  counter-reset: tablecontent;
}

.table-content-container.hidden .table-content-content {
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-content-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  background: transparent;
  color: #333;
}

.table-content-item:hover {
  background: rgba(74, 144, 226, 0.05);
  transform: translateX(4px);
}

.table-content-item:active {
  transform: translateX(2px);
}

.table-content-index {
  color: #666;
  font-size: 14px;
  min-width: 24px;
  text-align: right;
}

.table-content-preview {
  flex: 1;
  font-size: 14px;
  color: #1a1a1a;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-content-item:hover .table-content-preview {
  color: #4a90e2;
}

.table-content-empty-state {
  text-align: center;
  padding: 24px 16px;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin: 12px 0;
}

.table-content-empty-state::before {
  content: '📑';
  font-size: 32px;
  margin-bottom: 8px;
}

/* Custom scrollbar for content */
.table-content-content::-webkit-scrollbar {
  width: 6px;
}

.table-content-content::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.table-content-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.table-content-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Add fade effect at top and bottom of scrollable content */
.table-content-content::before,
.table-content-content::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  z-index: 1;
}

/* Add loading state for content */
.table-content-content.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Add loading spinner */
.table-content-content.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 2px solid #eaeaea;
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Table content button styles */
.table-content-button {
  background: #4a90e2;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  color: white;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
  opacity: 1;
  transform: scale(1);
  will-change: transform, opacity, box-shadow, background-color;
  display: flex;
  align-items: center;
  gap: 6px;
}

.table-content-button::before {
  content: '📑';
  font-size: 16px;
}

.table-content-button:hover {
  background: #357abd;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 12px rgba(74, 144, 226, 0.3);
}

.table-content-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
  transform: scale(0.98);
  box-shadow: none;
}

.table-content-button.hidden {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sections container */
.table-content-sections {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
  margin-right: -10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: calc(100vh - 120px);
  /* Account for header and padding */
}

/* Collapsible section styles */
.collapsible-section {
  background: none;
  border: none;
  border-radius: 0;
  box-shadow: none;
  margin-bottom: 32px;
  padding: 0;
  max-height: 50%;
  display: flex;
  flex-direction: column;
}

.collapsible-header {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0 0 8px 0;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  font-size: 18px;
  font-weight: 700;
  color: #111;
}

.collapsible-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  color: #111;
  gap: 8px;
}

.table-content-section .collapsible-title::before {
  content: '\2630';
  /* list icon */
  font-size: 22px;
  margin-right: 4px;
  font-weight: 400;
}

.bookmark-section .collapsible-title::before {
  content: '\1F516';
  /* bookmark icon */
  font-size: 22px;
  margin-right: 4px;
  font-weight: 400;
}

.collapsible-icon {
  margin-left: auto;
  font-size: 18px;
  color: #222;
  transition: transform 0.3s;
}

.collapsible-section.expanded .collapsible-icon {
  transform: rotate(180deg);
}

.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: max-height;
  display: flex;
  flex-direction: column;
}

.collapsible-section.expanded .collapsible-content {
  max-height: 1000px;
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
}

.table-content-content,
.bookmark-content {
  padding: 0;
  background: none;
  gap: 0;
  overflow-y: auto;
  flex: 1 1 auto;
  min-height: 0;
}

.table-content-item,
.bookmark-item {
  background: none;
  border: none;
  border-radius: 0;
  margin: 0 0 8px 0;
  padding: 0 0 0 0;
  font-size: 17px;
  color: #111;
  font-weight: 400;
  line-height: 1.7;
  cursor: pointer;
  transition: color 0.2s;
  position: relative;
  box-shadow: none;
}

.table-content-item:hover,
.bookmark-item:hover {
  color: #0070f3;
  background: none;
}

.table-content-item:last-child,
.bookmark-item:last-child {
  margin-bottom: 0;
}

.table-content-item::before {
  display: none;
}

.bookmark-clear-all {
  display: none;
}

hr.section-divider {
  border: none;
  border-top: 1px solid #eee;
  margin: 24px 0 24px 0;
}

/* Hide old empty state icon */
.table-content-empty-state::before,
.bookmark-empty-state::before {
  display: none;
}

.table-content-empty-state,
.bookmark-empty-state {
  background: none;
  color: #888;
  font-size: 15px;
  padding: 16px 0;
  text-align: left;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

/* Scrollbar styles */
.table-content-sections::-webkit-scrollbar {
  width: 6px;
}

.table-content-sections::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.table-content-sections::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.table-content-sections::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Button styles */
.table-content-button {
  background: #4a90e2;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  color: white;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
  opacity: 1;
  transform: scale(1);
  will-change: transform, opacity, box-shadow, background-color;
  display: flex;
  align-items: center;
  gap: 6px;
}

.table-content-button::before {
  content: '📑';
  font-size: 16px;
}

.table-content-button:hover {
  background: #357abd;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 12px rgba(74, 144, 226, 0.3);
}

.table-content-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
  transform: scale(0.98);
  box-shadow: none;
}

.table-content-button.hidden {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bookmark-button-container {
  position: absolute;
  left: -50px;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  opacity: 1;
}

.message-container {
  position: relative;
}

.bookmark-button {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  cursor: pointer;
  padding: 4px;
  color: #999;
  font-size: 16px;
  transition: all 0.15s ease;
  line-height: 1;
  width: 48px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  gap: 4px;
}

.bookmark-button .save-icon {
  font-size: 14px;
  line-height: 1;
  display: flex;
  align-items: center;
  color: #999;
  transition: color 0.15s;
}

.bookmark-button .ext-logo-icon {
  width: 14px;
  height: 14px;
  display: block;
  transition: filter 0.15s;
}

.bookmark-button:hover {
  background: #f5faff;
  border-color: #b3d1f7;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.10);
  color: #666;
  transform: scale(1.05);
}

.bookmark-button.active {
  background: #4caf50;
  border-color: #4caf50;
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.20);
}

.bookmark-button.active .save-icon {
  color: white;
}

.bookmark-button.active .ext-logo-icon {
  filter: brightness(0) invert(1);
}

.bookmark-button.active:hover {
  background: #388e3c;
  border-color: #388e3c;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.30);
}

/* Remove old pseudo-element icon logic */
.bookmark-button::before,
.bookmark-button::after {
  display: none !important;
  content: none !important;
}

.bookmark-clear-all {
  display: block;
  width: 100%;
  padding: 8px 16px;
  margin: 8px 0;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #dc3545;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bookmark-clear-all:hover {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

/* Bookmark section title + clear all icon */
.bookmark-section .collapsible-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 0 8px 0;
  margin-bottom: 8px;
  background: none;
  border: none;
  box-shadow: none;
  font-size: 18px;
  font-weight: 700;
  color: #111;
  position: relative;
}

.bookmark-clear-all {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #dc3545;
  font-size: 20px;
  margin-left: 4px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
}

.bookmark-clear-all:hover {
  background: #ffeaea;
  color: #b30000;
}

.bookmark-clear-all::before {
  content: '\1F5D1';
  /* trash icon */
  font-size: 20px;
}

.bookmark-item {
  background: none;
  border: none;
  border-radius: 0;
  margin: 0 0 12px 0;
  padding: 12px;
  font-size: 16px;
  color: #222;
  font-weight: 400;
  line-height: 1.7;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  box-shadow: none;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.bookmark-item:hover {
  color: #0070f3;
  background: #f0f7ff;
  border-color: #4a90e2;
  transform: translateX(2px);
}

.bookmark-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.bookmark-title {
  color: #1a1a1a;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 6px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.bookmark-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.bookmark-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.bookmark-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #bbdefb;
}

.bookmark-note {
  font-size: 12px;
  color: #666;
  font-style: italic;
  background: #fff3cd;
  padding: 6px 8px;
  border-radius: 4px;
  border-left: 3px solid #ffc107;
  margin-top: 4px;
}

.bookmark-meta {
  font-size: 11px;
  color: #888;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.bookmark-meta::before {
  content: '📅';
  font-size: 10px;
}

.bookmark-remove {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  cursor: pointer;
  padding: 2px 8px;
  border-radius: 4px;
  margin-left: 8px;
  opacity: 0.7;
  transition: background 0.2s, color 0.2s, opacity 0.2s;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.bookmark-remove:hover {
  background: #ffeaea;
  color: #b30000;
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .bookmark-item {
    padding: 8px;
    margin-bottom: 8px;
  }
  
  .bookmark-text {
    font-size: 13px;
    -webkit-line-clamp: 2;
  }
  
  .bookmark-tags {
    gap: 2px;
  }
  
  .bookmark-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}