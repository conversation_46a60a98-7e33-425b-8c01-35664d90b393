<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inline-like Prompt Optimizer Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #374151;
            line-height: 1.6;
        }

        .demo-container {
            max-width: 768px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .demo-title {
            font-size: 2rem;
            font-weight: 600;
            color: #10a37f;
            margin-bottom: 8px;
        }

        .demo-subtitle {
            color: #6b7280;
            font-size: 1rem;
        }

        .chat-messages {
            flex: 1;
            margin-bottom: 20px;
            overflow-y: auto;
            max-height: 60vh;
            padding: 20px 0;
        }

        .message {
            padding: 16px 20px;
            margin: 12px 0;
            max-width: 80%;
            border-radius: 18px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .message.user {
            background-color: #10a37f;
            color: #ffffff;
            margin-left: auto;
        }

        .message.assistant {
            background-color: #f7f7f8;
            color: #374151;
            margin-right: auto;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 8px;
            display: block;
        }

        .input-container {
            position: relative;
            background-color: #ffffff;
            border-radius: 12px;
            border: 1px solid #d1d5db;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .input-container:focus-within {
            border-color: #10a37f;
            box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
        }

        .chat-input {
            width: 100%;
            border: none;
            outline: none;
            font-size: 16px;
            padding-right: 40px;
            resize: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
        }

        .send-button {
            position: absolute;
            right: 8px;
            bottom: 8px;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background-color: #10a37f;
            color: #ffffff;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .send-button:hover:not(:disabled) {
            background-color: #0d8a6f;
        }

        .send-button:disabled {
            background-color: #d1d5db;
            color: #9ca3af;
            cursor: not-allowed;
        }

        .demo-instructions {
            text-align: center;
            margin-top: 20px;
            color: #6b7280;
            font-size: 14px;
        }

        .features-list {
            background-color: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }

        .features-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }

        .feature-item {
            background-color: #ffffff;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #374151;
        }

        .feature-description {
            font-size: 14px;
            color: #6b7280;
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 16px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">✨ Inline-like Prompt Optimizer</h1>
            <p class="demo-subtitle">Experience AI-powered prompt optimization in action</p>
        </div>

        <div class="features-list">
            <h3 class="features-title">Key Features</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">💬</div>
                    <div class="feature-title">Smart Input Detection</div>
                    <div class="feature-description">Automatically detects when you start typing and shows the optimize button</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✨</div>
                    <div class="feature-title">One-Click Optimization</div>
                    <div class="feature-description">Click the optimize button to get multiple improved prompt suggestions</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📋</div>
                    <div class="feature-title">Multiple Actions</div>
                    <div class="feature-description">Replace, copy, or insert optimized prompts with ease</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">Smart Suggestions</div>
                    <div class="feature-description">Get detailed, clear, and concise versions of your prompts</div>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div>Hello! I'm ChatGPT. How can I help you today?</div>
                <span class="message-time">Just now</span>
            </div>
        </div>

        <div class="input-container">
            <textarea 
                class="chat-input" 
                id="chatInput"
                placeholder="Message ChatGPT..."
                rows="1"
            ></textarea>
            <button class="send-button" id="sendButton" disabled>
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>

        <div class="demo-instructions">
            💡 Start typing to see the ✨ Optimize button appear!
        </div>
    </div>

    <script>
        // Demo functionality
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');

        // Auto-resize textarea
        chatInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            // Enable/disable send button
            sendButton.disabled = !this.value.trim();
        });

        // Handle Enter key
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            chatInput.value = '';
            chatInput.style.height = 'auto';
            sendButton.disabled = true;

            // Simulate AI response
            setTimeout(() => {
                addMessage(`I understand you're asking about: "${message}". This is a simulated response to demonstrate the chat interface. In a real ChatGPT implementation, you would get an actual AI-generated response here.`, 'assistant');
            }, 1500);
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const textDiv = document.createElement('div');
            textDiv.textContent = text;
            
            const timeSpan = document.createElement('span');
            timeSpan.className = 'message-time';
            timeSpan.textContent = new Date().toLocaleTimeString();
            
            messageDiv.appendChild(textDiv);
            messageDiv.appendChild(timeSpan);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Simulate typing indicator
        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message assistant';
            typingDiv.innerHTML = '<div style="font-style: italic; opacity: 0.7;">ChatGPT is typing...</div>';
            typingDiv.id = 'typing-indicator';
            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
    </script>
</body>
</html> 