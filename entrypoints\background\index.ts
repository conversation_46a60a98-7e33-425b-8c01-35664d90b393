/// <reference types="chrome"/>

import { handleGoogleSignIn, saveAuthState, clearAuthState } from '@/utils/auth';
import { promptCircleApi } from '@/utils/promptCircleApi';

export default defineBackground({
  main() {
    // Handle messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('Background received message:', message);
      
      if (message.type === 'START_GOOGLE_LOGIN') {
        handleGoogleLogin()
          .then((result) => {
            // Send success response back to popup using sendResponse
            sendResponse({
              type: 'GOOGLE_LOGIN_RESULT',
              success: true,
              authState: result
            });
          })
          .catch((error) => {
            // Send error response back to popup using sendResponse
            sendResponse({
              type: 'GOOGLE_LOGIN_RESULT',
              success: false,
              error: error.message
            });
          });
        return true; // Keep the message channel open for async response
      }

      // Handle PromptCircle API calls
      if (message.type === 'PROMPTCIRCLE_CHECK_AUTH') {
        promptCircleApi.checkAuth()
          .then((result) => {
            sendResponse({
              type: 'PROMPTCIRCLE_AUTH_RESULT',
              success: true,
              isAuthenticated: result.isAuthenticated
            });
          })
          .catch((error) => {
            sendResponse({
              type: 'PROMPTCIRCLE_AUTH_RESULT',
              success: false,
              error: error.message
            });
          });
        return true;
      }

      if (message.type === 'PROMPTCIRCLE_OPTIMIZE_PROMPT') {
        promptCircleApi.optimizePrompt(message.prompt)
          .then((result) => {
            sendResponse({
              type: 'PROMPTCIRCLE_OPTIMIZE_RESULT',
              success: true,
              data: result
            });
          })
          .catch((error) => {
            sendResponse({
              type: 'PROMPTCIRCLE_OPTIMIZE_RESULT',
              success: false,
              error: error.message
            });
          });
        return true;
      }

      if (message.type === 'PROMPTCIRCLE_LOGOUT') {
        promptCircleApi.logout()
          .then(() => {
            sendResponse({
              type: 'PROMPTCIRCLE_LOGOUT_RESULT',
              success: true
            });
          })
          .catch((error) => {
            sendResponse({
              type: 'PROMPTCIRCLE_LOGOUT_RESULT',
              success: false,
              error: error.message
            });
          });
        return true;
      }

      if (message.type === 'PROMPTCIRCLE_OPEN_LOGIN') {
        promptCircleApi.openLoginPage();
        sendResponse({ success: true });
        return false;
      }

      if (message.action === 'openPopup') {
        // @ts-ignore
        if (chrome.action && chrome.action.openPopup) {
          // @ts-ignore
          chrome.action.openPopup();
        } else {
          console.warn('chrome.action.openPopup() is not available.');
        }
        return false;
      }

      // Handle fillPrompt action - forward to content script
      if (message.action === 'fillPrompt' && message.tabId) {
        console.log('Background forwarding fillPrompt to tab:', message.tabId);
        chrome.tabs.sendMessage(message.tabId, {
          action: 'fillPrompt',
          prompt: message.prompt,
          selector: message.selector
        }, (response) => {
          console.log('Content script response:', response);
          sendResponse(response);
        });
        return true; // Keep the message channel open for async response
      }

      // Handle openAndFillPrompt action
      if (message.action === 'openAndFillPrompt') {
        const { url, prompt, selector } = message;
        chrome.tabs.create({ url }, async (tab) => {
          if (!tab.id) {
            sendResponse({ error: 'Failed to create tab.' });
            return;
          }
          // Inject the content script programmatically
          chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content-scripts/content.js']
          }, async () => {
            // Wait for handshake from content script
            const waitForContentScriptReady = (tabId: number, timeout = 10000) => {
              return new Promise((resolve, reject) => {
                let timer: ReturnType<typeof setTimeout>;
                function onMessage(msg: any, sender: any) {
                  if (
                    msg.action === 'contentScriptReady' &&
                    sender.tab &&
                    sender.tab.id === tabId
                  ) {
                    chrome.runtime.onMessage.removeListener(onMessage);
                    clearTimeout(timer);
                    resolve(undefined);
                  }
                }
                chrome.runtime.onMessage.addListener(onMessage);
                timer = setTimeout(() => {
                  chrome.runtime.onMessage.removeListener(onMessage);
                  reject(new Error('Content script not ready in time'));
                }, timeout);
              });
            };
            try {
              await waitForContentScriptReady(tab.id!);
              chrome.tabs.sendMessage(tab.id!, {
                action: 'fillPrompt',
                prompt,
                selector
              }, (response) => {
                sendResponse({ success: true, response });
              });
            } catch (err) {
              sendResponse({ error: 'Content script not ready in time.' });
            }
          });
        });
        return true; // Keep the message channel open for async response
      }
    });

    // Handle extension installation or update
    // @ts-ignore
    chrome.runtime.onInstalled.addListener(() => {
      console.log('Background: Extension installed/updated');
      // Clear any existing auth state
      clearAuthState();
    });
  }
});

// Handle Google login process
async function handleGoogleLogin() {
  try {
    console.log('Background: Starting Google sign-in process');
    const authState = await handleGoogleSignIn();
    console.log('Background: Google sign-in successful', authState);
    
    // Broadcast auth state change to all extension contexts with error handling
    try {
      chrome.runtime.sendMessage({
        type: 'AUTH_STATE_CHANGED',
        authState
      });
    } catch (error) {
      // Ignore errors when no receivers are available
      console.log('No receivers available for AUTH_STATE_CHANGED broadcast');
    }
    
    // Send message to all tab content scripts with error handling
    // @ts-ignore
    if (chrome.tabs && chrome.tabs.query) {
      try {
        // @ts-ignore
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
          if (tab.id) {
            chrome.tabs.sendMessage(tab.id, {
              type: 'AUTH_STATE_CHANGED',
              authState
            });
          }
        }
      } catch (error) {
        console.log('Failed to send message to tabs:', error);
      }
    }
    
    return authState;
  } catch (error) {
    console.error('Background: Google sign-in failed', error);
    throw error;
  }
} 