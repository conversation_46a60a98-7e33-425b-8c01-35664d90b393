import { addBookmarkWithMetadata, generateTitleFromContent, generateTagSuggestions } from '../../utils/bookmark';

export interface BookmarkDialogOptions {
  content: any;
  node: Element;
  onSave?: () => void;
  onCancel?: () => void;
}

export class BookmarkDialog {
  private dialog: HTMLElement | null = null;
  private options: BookmarkDialogOptions;
  private suggestedTags: string[] = [];

  constructor(options: BookmarkDialogOptions) {
    this.options = options;
  }

  async show(): Promise<void> {
    // Generate tag suggestions before showing dialog
    this.suggestedTags = await generateTagSuggestions(this.options.content);
    this.createDialog();
    document.body.appendChild(this.dialog!);
    this.setupEventListeners();
    
    // Focus on the first input
    const firstInput = this.dialog!.querySelector('input') as HTMLInputElement;
    if (firstInput) {
      firstInput.focus();
    }
  }

  private createDialog(): void {
    this.dialog = document.createElement('div');
    this.dialog.className = 'bookmark-dialog-overlay';
    this.dialog.innerHTML = `
      <div class="bookmark-dialog">
        <div class="bookmark-dialog-header">
          <h3>Save Response</h3>
          <button class="bookmark-dialog-close" title="Close">×</button>
        </div>
        <div class="bookmark-dialog-content">
          <div class="bookmark-dialog-section">
            <label for="bookmark-title">📝 Title</label>
            <input 
              type="text" 
              id="bookmark-title" 
              placeholder="Enter a title for this response..."
              class="bookmark-dialog-input"
            >
          </div>
          <div class="bookmark-dialog-section">
            <label for="bookmark-tags">🏷️ Tags</label>
            <input 
              type="text" 
              id="bookmark-tags" 
              placeholder="Type tags or click suggestions below..."
              class="bookmark-dialog-input"
            >
            ${this.suggestedTags.length > 0 ? `
              <div class="bookmark-tag-suggestions">
                <label>Suggested tags:</label>
                <div class="bookmark-tag-suggestion-list">
                  ${this.suggestedTags.map(tag => `
                    <button class="bookmark-tag-suggestion" data-tag="${tag}">${tag}</button>
                  `).join('')}
                </div>
              </div>
            ` : ''}
          </div>
          <div class="bookmark-dialog-section">
            <label for="bookmark-note">🗒️ Note (optional)</label>
            <textarea 
              id="bookmark-note" 
              placeholder="Add context or reminder (e.g., 'use this in next pitch deck')..."
              class="bookmark-dialog-textarea"
              rows="3"
            ></textarea>
          </div>
          <div class="bookmark-dialog-preview">
            <label>Preview:</label>
            <div class="bookmark-dialog-preview-content">
              ${this.truncateContent(this.options.content, 200)}
            </div>
          </div>
        </div>
        <div class="bookmark-dialog-actions">
          <button class="bookmark-dialog-cancel">Cancel</button>
          <button class="bookmark-dialog-save">Save Response</button>
        </div>
      </div>
    `;
  }

  private setupEventListeners(): void {
    if (!this.dialog) return;

    // Close button
    const closeBtn = this.dialog.querySelector('.bookmark-dialog-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.close());
    }

    // Cancel button
    const cancelBtn = this.dialog.querySelector('.bookmark-dialog-cancel');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.close());
    }

    // Save button
    const saveBtn = this.dialog.querySelector('.bookmark-dialog-save');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.handleSave());
    }

    // Tag suggestion buttons
    const tagSuggestions = this.dialog.querySelectorAll('.bookmark-tag-suggestion');
    tagSuggestions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tag = (e.target as HTMLElement).getAttribute('data-tag');
        if (tag) {
          this.addTag(tag);
        }
      });
    });

    // Close on overlay click
    this.dialog.addEventListener('click', (e) => {
      if (e.target === this.dialog) {
        this.close();
      }
    });

    // Handle Enter key in inputs
    const inputs = this.dialog.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.addEventListener('keydown', (e: Event) => {
        const keyEvent = e as KeyboardEvent;
        if (keyEvent.key === 'Enter' && !keyEvent.shiftKey) {
          e.preventDefault();
          this.handleSave();
        }
        if (keyEvent.key === 'Escape') {
          this.close();
        }
      });
    });

    // Handle tag input with comma separation
    const tagsInput = this.dialog.querySelector('#bookmark-tags') as HTMLInputElement;
    if (tagsInput) {
      tagsInput.addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ',') {
          e.preventDefault();
          const value = tagsInput.value.trim();
          if (value) {
            this.addTag(value);
            tagsInput.value = '';
          }
        }
      });
    }
  }

  private addTag(tag: string): void {
    const tagsInput = this.dialog!.querySelector('#bookmark-tags') as HTMLInputElement;
    const currentTags = tagsInput.value
      .split(',')
      .map(t => t.trim())
      .filter(t => t.length > 0);
    
    if (!currentTags.includes(tag)) {
      currentTags.push(tag);
      tagsInput.value = currentTags.join(', ');
    }
  }

  private async handleSave(): Promise<void> {
    if (!this.dialog) return;

    const titleInput = this.dialog.querySelector('#bookmark-title') as HTMLInputElement;
    const tagsInput = this.dialog.querySelector('#bookmark-tags') as HTMLInputElement;
    const noteInput = this.dialog.querySelector('#bookmark-note') as HTMLTextAreaElement;
    const title = titleInput.value.trim() || generateTitleFromContent(this.options.content);
    const tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    const note = noteInput.value.trim() || undefined;

    try {
      await addBookmarkWithMetadata(
        this.options.content,
        this.options.node,
        title,
        tags,
        note
      );

      // Show success feedback
      this.showSuccessFeedback();
      
      // Call onSave callback
      if (this.options.onSave) {
        this.options.onSave();
      }

      // Close dialog after a short delay
      setTimeout(() => this.close(), 1000);
    } catch (error) {
      console.error('Error saving bookmark:', error);
      this.showErrorFeedback();
    }
  }

  private showSuccessFeedback(): void {
    if (!this.dialog) return;

    const saveBtn = this.dialog.querySelector('.bookmark-dialog-save') as HTMLButtonElement;
    if (saveBtn) {
      const originalText = saveBtn.textContent;
      saveBtn.textContent = '✓ Saved!';
      saveBtn.style.backgroundColor = '#4CAF50';
      saveBtn.disabled = true;
    }
  }

  private showErrorFeedback(): void {
    if (!this.dialog) return;

    const saveBtn = this.dialog.querySelector('.bookmark-dialog-save') as HTMLButtonElement;
    if (saveBtn) {
      const originalText = saveBtn.textContent;
      saveBtn.textContent = 'Error!';
      saveBtn.style.backgroundColor = '#f44336';
      saveBtn.disabled = true;
      
      setTimeout(() => {
        saveBtn.textContent = originalText;
        saveBtn.style.backgroundColor = '';
        saveBtn.disabled = false;
      }, 2000);
    }
  }

  private close(): void {
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
      this.dialog = null;
    }
    
    if (this.options.onCancel) {
      this.options.onCancel();
    }
  }

  private truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }
    return content.substring(0, maxLength) + '...';
  }
}

// Add CSS styles for the dialog
const dialogStyles = `
.bookmark-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease-out;
}

.bookmark-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.bookmark-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.bookmark-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.bookmark-dialog-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.bookmark-dialog-close:hover {
  background: #f5f5f5;
  color: #333;
}

.bookmark-dialog-content {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.bookmark-dialog-section {
  margin-bottom: 20px;
}

.bookmark-dialog-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.bookmark-dialog-input,
.bookmark-dialog-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.bookmark-dialog-input:focus,
.bookmark-dialog-textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.bookmark-dialog-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.bookmark-tag-suggestions {
  margin-top: 8px;
}

.bookmark-tag-suggestions label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.bookmark-tag-suggestion-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.bookmark-tag-suggestion {
  background: #f0f8ff;
  border: 1px solid #4a90e2;
  color: #4a90e2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.bookmark-tag-suggestion:hover {
  background: #4a90e2;
  color: white;
  transform: translateY(-1px);
}

.bookmark-tag-suggestion:active {
  transform: translateY(0);
}

.bookmark-dialog-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

.bookmark-dialog-preview label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.bookmark-dialog-preview-content {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  max-height: 100px;
  overflow-y: auto;
}

.bookmark-dialog-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #e0e0e0;
  justify-content: flex-end;
}

.bookmark-dialog-cancel,
.bookmark-dialog-save {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.bookmark-dialog-cancel {
  background: #f5f5f5;
  color: #666;
}

.bookmark-dialog-cancel:hover {
  background: #e0e0e0;
  color: #333;
}

.bookmark-dialog-save {
  background: #4a90e2;
  color: white;
}

.bookmark-dialog-save:hover:not(:disabled) {
  background: #357abd;
}

.bookmark-dialog-save:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
`;

// Inject styles
const styleElement = document.createElement('style');
styleElement.textContent = dialogStyles;
document.head.appendChild(styleElement); 