// Interface for message selectors
export interface MessageSelectors {
  container: string;  // Main conversation container
  userSelector: string;  // User message node selector
  userSelectorText: string;  // User message content selector
  assistantSelector: string;  // Assistant message block selector
  assistantContentWrapper?: string;  // Wrapper element containing list of paragraphs (optional)
  assistantSelectorText: string;  // Actual content selector (usually p tags)
  submitButtonSelector?: string; // Submit button selector
  submitButtonLoadingState?: string; // Selector or attribute to check if submit button is in loading state
}

export interface ChatbotConfig {
  name: string;
  urlCheck: (url: string) => boolean;
  selectors: string[];
  parentSelector: string | string[];
  buttonContainerClass: string;
  customButtonContainer?: boolean;
  buttonStyle?: string;
  buttonHoverStyle?: string;
  messageSelectors: MessageSelectors;
}

// Chatbot configurations
export const CHATBOTS_CONFIG: ChatbotConfig[] = [
  {
    name: 'Perplexity',
    selectors: [
      'textarea.overflow-auto',
      'textarea[placeholder="Ask anything..."]',
      'textarea.w-full.font-sans',
      'div[role="textbox"]'
    ],
    parentSelector: [
      'div.bg-background.w-full.outline-none.focus\\:outline-none.focus\\:ring-borderMain',
      'div.grid-rows-1fr-auto.grid.grid-cols-3',
      'div.col-start-1.col-end-4'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-perplexity',
    urlCheck: (url: string) => url.includes('perplexity.ai'),
    messageSelectors: {
      container: '.message-container',
      userSelector: '[data-message-role="user"]',
      userSelectorText: '.message-content',
      assistantSelector: '[data-message-role="assistant"]',
      assistantSelectorText: 'p, li, pre, code, ul, ol, blockquote',
      // assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'GoogleAIStudio',
    selectors: [
      'textarea.textarea.gmat-body-medium[placeholder="Type something"]',
      'ms-autosize-textarea textarea[placeholder="Type something"]',
      '.prompt-input-wrapper textarea'
    ],
    parentSelector: [
      'div.prompt-input-wrapper',
      'div.prompt-input-wrapper-container',
      'div.text-wrapper'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-googleaistudio',
    urlCheck: (url: string) => url.includes('aistudio.google.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '[data-message-role="user"]',
      userSelectorText: '.message-content',
      assistantSelector: '[data-message-role="model"]',
      assistantSelectorText: 'p, li, pre, code, ul, ol, blockquote',
      // assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'ChatGPT',
    selectors: [
      '#prompt-textarea',
      'textarea[data-id="root"]',
      '.chat-input-textarea',
      'div[data-testid="chat-input-textbox"]',
      'div.ProseMirror[contenteditable="true"]',
      'textarea[placeholder="Message ChatGPT"]'
    ],
    parentSelector: 'div#composer-background, form',
    buttonContainerClass: 'prompt-perfect-buttons-chatgpt',
    urlCheck: (url: string) => url.includes('chatgpt.com'),
    messageSelectors: {
      container: '#thread > div',
      userSelector: 'div[data-message-author-role="user"]',
      userSelectorText: '.whitespace-pre-wrap',
      assistantSelector: 'div[data-message-author-role="assistant"]',
      assistantContentWrapper: '.markdown',
      assistantSelectorText: 'p, li, pre, code, ul, ol, blockquote',
      submitButtonSelector: 'button[data-testid="send-button"]',
      submitButtonLoadingState: 'button[data-testid="send-button"][disabled]'
    }
  },
  {
    name: 'Gemini',
    selectors: [
      'div[contenteditable="true"]',
      'div[role="textbox"]',
      'div.ProseMirror[contenteditable="true"]',
      '.ql-editor[contenteditable="true"]',
      'textarea[placeholder="Enter a prompt"]',
      '#prompt-textarea'
    ],
    parentSelector: 'div.relative.flex.w-full.grow.flex-col.items-stretch, div.relative.flex.grow.flex-col.items-stretch, div.input-area-container, div.relative.flex.h-full.w-full.flex-col',
    buttonContainerClass: 'prompt-perfect-buttons-gemini',
    urlCheck: (url: string) => url.includes('gemini.google.com'),
    messageSelectors: {
      container: '#chat-history > infinite-scroller',
      userSelector: '#user-query-content-0',
      userSelectorText: '#user-query-content-0 > span > span > div > p',
      assistantSelector: '#model-response > div > response-container > div',
      assistantContentWrapper: '#model-response-message-contentr_6bf4a2e1fc667f33',
      assistantSelectorText: 'p, li, pre, code, ul, ol, blockquote'
    }
  },
  {
    name: 'Claude',
    selectors: [
      'div[contenteditable="true"][translate="no"][enterkeyhint="enter"]',
      '.ProseMirror[contenteditable="true"]'
    ],
    parentSelector: [
      'div.flex.flex-col.bg-bg-000.gap-1\\.5.border-0\\.5.border-border-300.pl-4.pt-2\\.5.pr-2\\.5.pb-2\\.5.-mx-1.sm\\:mx-0.items-stretch.transition-all.duration-200.relative.shadow-\\[0_0\\.25rem_1\\.25rem_rgba\\(0\\,0\\,0\\,0\\.035\\)\\].focus-within\\:shadow-\\[0_0\\.25rem_1\\.25rem_rgba\\(0\\,0\\,0\\,0\\.075\\)\\].hover\\:border-border-200.focus-within\\:border-border-200.cursor-text.z-10.rounded-t-2xl.border-b-0',
      'div.flex.flex-col.bg-bg-000.gap-1\\.5.border-0\\.5.border-border-300.pl-4.pt-2\\.5.pr-2\\.5.pb-2\\.5.sm\\:mx-0.items-stretch.transition-all.duration-200.relative.shadow-\\[0_0\\.25rem_1\\.25rem_rgba\\(0\\,0\\,0\\,0\\.035\\)\\].focus-within\\:shadow-\\[0_0\\.25rem_1\\.25rem_rgba\\(0\\,0\\,0\\,0\\.075\\)\\].hover\\:border-border-200.focus-within\\:border-border-200.cursor-text.z-10.rounded-2xl',
      'div.flex.flex-col.bg-bg-000'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-claude',
    urlCheck: (url: string) => url.includes('claude.ai'),
    messageSelectors: {
      container: 'div.overflow-y-auto.flex-1 > div',
      userSelector: 'div.w-full.text-token-text-primary[data-message-author-role="user"]',
      userSelectorText: '.prose',
      assistantSelector: 'div.w-full.text-token-text-primary[data-message-author-role="assistant"]',
      assistantContentWrapper: '.prose',
      assistantSelectorText: 'p, li, pre, code, ul, ol, blockquote'
    }
  },
  {
    name: 'Copilot',
    selectors: [
      'textarea#userInput',
      'textarea[placeholder="Message Copilot"]',
      '.block.min-h-user-input.w-full.resize-none'
    ],
    parentSelector: 'div.w-expanded-composer',
    buttonContainerClass: 'prompt-perfect-buttons-copilot',
    urlCheck: (url: string) => url.includes('copilot.microsoft.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'Grok',
    selectors: [
      'textarea.w-full.px-2.\\@\\[480px\\]\\/input\\:px-3.bg-transparent.focus\\:outline-none.text-primary.align-bottom',
      'textarea[aria-label="Ask Grok anything"]',
      'div.query-bar textarea'
    ],
    parentSelector: [
      'div.query-bar',
      'div.flex.flex-row.gap-2.justify-center.w-full.relative'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-grok',
    urlCheck: (url: string) => url.includes('grok.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'GrokX',
    selectors: [
      'textarea.r-30o5oe.r-1dz5y72.r-1niwhzg.r-17gur6a.r-1yadl64.r-deolkf.r-homxoj.r-poiln3.r-1ny4l3l.r-xyw6el.r-13awgt0.r-ubezar.r-k8qxaj.r-1knelpx.r-13qz1uu.r-fdjqy7',
      'textarea[placeholder="Ask anything"]'
    ],
    parentSelector: [
      'div.css-175oi2r.r-1yadl64.r-eqz5dr.r-16y2uox.r-1wbh5a2.r-1niwhzg.r-13awgt0.r-ubezar.r-erd55g.r-1wtj0ep.r-1noe1sz.r-13qz1uu',
      'div.css-175oi2r'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-grok-x',
    urlCheck: (url: string) => url.includes('x.com/i/grok'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'NotebookLM',
    selectors: [
      'textarea.query-box-input'
    ],
    parentSelector: 'div.omnibar-container',
    buttonContainerClass: 'prompt-perfect-buttons-notebooklm',
    urlCheck: (url: string) => url.includes('notebooklm.google.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'ProjectIDX',
    selectors: [
      'textarea.l',
      'textarea[placeholder="Enter a prompt or \'/\' for commands"]'
    ],
    parentSelector: 'div.Tf.is.R.Pf',
    buttonContainerClass: 'prompt-perfect-buttons-projectidx',
    urlCheck: (url: string) => url.includes('idx.google.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  },
  {
    name: 'DeepSeek',
    selectors: [
      'textarea#chat-input',
      'textarea._27c9245',
      'textarea[placeholder="Message DeepSeek"]'
    ],
    parentSelector: [
      'div.dd442025._42b6996'
    ],
    buttonContainerClass: 'prompt-perfect-buttons-deepseek',
    urlCheck: (url: string) => url.includes('chat.deepseek.com'),
    messageSelectors: {
      container: '.conversation-container',
      userSelector: '.user-message',
      userSelectorText: '.message-content',
      assistantSelector: '.assistant-message',
      assistantSelectorText: '.message-content'
    }
  }
];

// Get current chatbot based on URL
export function getCurrentChatbot(): ChatbotConfig | null {
  const currentUrl = window.location.href;
  return CHATBOTS_CONFIG.find(chatbot => chatbot.urlCheck(currentUrl)) || null;
}

// Get messages using chatbot-specific selectors
export function getMessages(chatbot: ChatbotConfig) {
  const container = document.querySelector(chatbot.messageSelectors.container);
  console.log("Finding messages in container:", container);
  if (!container) {
    console.log('Message container not found');
    return { userMessages: [], assistantMessages: [] };
  }

  const userMessages = container.querySelectorAll(chatbot.messageSelectors.userSelector);
  const assistantMessages = container.querySelectorAll(chatbot.messageSelectors.assistantSelector);

  console.log("User messages found:", userMessages.length);
  console.log("Assistant messages found:", assistantMessages.length);

  return {
    userMessages,
    assistantMessages
  };
}

// Get message content using chatbot-specific selectors
export function getMessageContent(message: Element, isUser: boolean, chatbot: ChatbotConfig): string {
  if (isUser) {
    const contentElement = message.querySelector(chatbot.messageSelectors.userSelectorText);
    return contentElement?.textContent?.trim() || '';
  }

  // Handle assistant message content
  const { assistantContentWrapper } = chatbot.messageSelectors;
  let elements: Element[] = [];
  if (assistantContentWrapper) {
    const wrapper = message.querySelector(assistantContentWrapper);
    if (wrapper) {
      elements = Array.from(wrapper.querySelectorAll('*'));
      // elements = Array.from(wrapper.children);
      // elements = Array.from(wrapper.querySelectorAll('p, li, pre, code, ul, ol, blockquote, h1, h2, h3, h4, h5, h6'));
    }
  }
  // Fallback: use all children of the message element
  if (elements.length === 0) {
    elements = Array.from(message.children);
  }
  // If still nothing, fallback to the message itself
  if (elements.length === 0) {
    elements = [message];
  }
  // Join all block-level elements' text, preserving order and line breaks
  const fullContent = elements
    .map(el => el.textContent?.trim() || '')
    .filter(Boolean)
    .join('\n');
  return fullContent;
}

// Add helper function to check submit button state
export function isSubmitButtonLoading(chatbot: ChatbotConfig): boolean {
  if (!chatbot.messageSelectors.submitButtonLoadingState) return false;
  
  const loadingButton = document.querySelector(chatbot.messageSelectors.submitButtonLoadingState);
  return !!loadingButton;
}