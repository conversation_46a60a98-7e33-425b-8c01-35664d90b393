# ✨ Inline-like Prompt Optimizer Solution

A comprehensive browser extension that provides inline-like prompt optimization functionality for AI chatbots like <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and more.

## 🎯 Features

### 1. **LLM Input View (ChatGPT Simulation)**
- Simulates ChatGPT's textarea interface with proper styling
- Responsive design that matches the original ChatGPT UI
- Auto-resizing textarea with proper focus states
- Send button with loading states

### 2. **Optimize Button Overlay**
- Small "✨ Optimize" button appears when user starts typing
- Positioned absolutely next to the input field
- Smooth fade-in/out animations
- Glassmorphism design with backdrop blur
- Hover effects and loading states

### 3. **Inline Flyout Panel**
- Popup panel that appears when optimize button is clicked
- Shows original prompt in a read-only field
- Displays 2-3 optimized prompt suggestions with different styles:
  - **Detailed**: Comprehensive prompts with examples
  - **Clear**: Concise but comprehensive prompts
  - **Concise**: Brief, focused prompts
- Action buttons for each suggestion:
  - **Replace**: Replaces the original prompt
  - **Copy**: Copies to clipboard
  - **Insert Below**: Adds the optimized prompt below the original

### 4. **Inline Confirmation Toast**
- Small toast notifications appear in bottom-right corner
- Shows success messages like "Prompt replaced successfully!" or "Copied to clipboard!"
- Auto-dismisses after 3 seconds
- Smooth slide-in/out animations

## 🏗️ Architecture

### Core Components

#### 1. **InlineOptimizer Component** (`inline-optimizer.tsx`)
```typescript
interface InlineOptimizerProps {
  inputField: HTMLElement;
  onClose: () => void;
}
```

**Key Features:**
- React component with Material-UI styling
- State management for button visibility, flyout, and loading
- Event listeners for input changes and click outside
- Mock optimization function (replaceable with real API)
- Toast notification system

#### 2. **Integration System** (`inline-integration.ts`)
```typescript
class InlinePromptOptimizer {
  private integrations: Map<string, InlineIntegration>;
  private setupOptimizer(): void;
  private createIntegration(): void;
  private setupObserver(): void;
}
```

**Key Features:**
- Automatic detection of supported chatbot platforms
- Dynamic injection into existing input fields
- MutationObserver for handling dynamic content
- Cleanup and memory management
- Support for multiple input fields per page

#### 3. **Demo Interface** (`demo-chatgpt.tsx`)
```typescript
const DemoChatGPT: React.FC = () => {
  // ChatGPT-like interface with message history
  // Real-time chat simulation
  // Integration with InlineOptimizer
}
```

**Key Features:**
- Complete ChatGPT UI simulation
- Message history with timestamps
- Real-time chat functionality
- Responsive design
- Integration showcase

## 🎨 Design System

### Styled Components
- **OptimizeButton**: Glassmorphism design with hover effects
- **FlyoutPanel**: Floating panel with backdrop blur
- **OptimizedSuggestion**: Interactive suggestion cards
- **ActionButton**: Contextual button styles (replace/copy/insert)
- **ToastContainer**: Fixed positioning with animations

### Color Scheme
- Primary: `#1976d2` (Blue)
- Success: `#4caf50` (Green)
- Warning: `#ff9800` (Orange)
- Background: `rgba(255, 255, 255, 0.98)` with backdrop blur
- Border: `#e0e0e0` with subtle shadows

### Typography
- Font Family: System fonts with fallbacks
- Sizes: 12px (buttons), 13px (suggestions), 14px (body), 16px (headings)
- Weights: 400 (normal), 500 (medium), 600 (semibold)

## 🚀 Usage

### 1. **Basic Integration**
```typescript
import InlineOptimizer from './inline-optimizer';

// For any input field
const inputField = document.querySelector('#prompt-textarea');
if (inputField) {
  const optimizer = new InlineOptimizer({
    inputField,
    onClose: () => console.log('Optimizer closed')
  });
}
```

### 2. **Automatic Detection**
```typescript
import { inlineOptimizer } from './inline-integration';

// Automatically detects and integrates with supported platforms
inlineOptimizer.initialize();
```

### 3. **Custom Configuration**
```typescript
// Add new chatbot support in utils/configs.ts
{
  name: 'CustomAI',
  selectors: ['textarea.custom-input', 'input[type="text"]'],
  parentSelector: 'div.input-container',
  buttonContainerClass: 'inline-buttons-custom',
  urlCheck: (url: string) => url.includes('customai.com')
}
```

## 🔧 Configuration

### Supported Platforms
- **ChatGPT** (`chatgpt.com`)
- **Claude** (`claude.ai`)
- **Gemini** (`gemini.google.com`)
- **Perplexity** (`perplexity.ai`)
- **Google AI Studio** (`aistudio.google.com`)
- **Copilot** (`copilot.microsoft.com`)
- **Grok** (`grok.com`)
- **DeepSeek** (`chat.deepseek.com`)
- And more...

### Environment Variables
```bash
# Required for OpenAI integration
WXT_OPENAI_API_KEY=your_openai_api_key

# Required for Gemini integration
WXT_GEMINI_API_KEY=your_gemini_api_key
```

## 🎯 User Experience Flow

1. **User starts typing** → Optimize button appears
2. **User clicks "✨ Optimize"** → Flyout panel opens with loading state
3. **Optimization completes** → 3 suggestions displayed with type labels
4. **User selects suggestion** → Highlighted selection
5. **User clicks action button** → Toast notification + action executed
6. **User clicks outside** → Flyout closes automatically

## 🛠️ Development

### Prerequisites
- Node.js 18+
- WXT (Web Extension Tools)
- React 19+
- Material-UI 7+

### Installation
```bash
npm install
npm run dev
```

### Building
```bash
npm run build
npm run zip
```

### Testing
```bash
# Open demo page
open public/demo.html

# Test on supported platforms
npm run dev
# Then visit chatgpt.com, claude.ai, etc.
```

## 🔮 Future Enhancements

### Planned Features
- **Real AI Integration**: Replace mock optimization with actual API calls
- **Custom Prompts**: Allow users to define their own optimization styles
- **History**: Save and reuse optimized prompts
- **Analytics**: Track optimization usage and effectiveness
- **Keyboard Shortcuts**: Quick access via keyboard commands
- **Dark Mode**: Support for dark theme interfaces
- **Mobile Support**: Optimized for mobile browsers

### API Integration
```typescript
// Example real optimization function
async function optimizePrompt(prompt: string): Promise<OptimizedPrompt[]> {
  const response = await fetch('/api/optimize', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt, styles: ['detailed', 'clear', 'concise'] })
  });
  
  return response.json();
}
```

## 📱 Browser Compatibility

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+

## 🎨 Customization

### Styling
All components use Material-UI's styled API for easy customization:

```typescript
const CustomOptimizeButton = styled(OptimizeButton)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  borderRadius: '20px',
  // Add your custom styles
}));
```

### Theming
```typescript
const customTheme = createTheme({
  palette: {
    primary: { main: '#your-color' },
    // Customize colors
  },
  components: {
    // Customize component styles
  }
});
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For questions or issues:
- Create an issue on GitHub
- Check the demo page for examples
- Review the configuration documentation

---

**✨ Happy Prompt Optimizing! ✨** 