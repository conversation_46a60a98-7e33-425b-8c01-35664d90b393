import { defineConfig } from "wxt";
import { CONTENT_SCRIPT_MATCHES } from "./utils/matches";
import { OAUTH_CONFIG } from "./utils/constants";

// See https://wxt.dev/api/config.html
export default defineConfig({
  modules: ["@wxt-dev/module-react"],
  manifest: {
    permissions: [
      "activeTab",
      "storage",
      "identity",
      "tabs",
      "cookies",
      "scripting"
    ],
    // This key will keep the extension ID constant during development
    key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAglKFI+x2MoSn9U86UwEY+fMnvz38uTrxtcnerXYy+ArCfdjoippS0RlLYt/MxvvRwfAMdBslF0gwi7Wa10klc1oj6n0JQDLQax20tNEoNQ2oRonMgnXOWCIl0uioYVi+vBo6RsmA0+Zy4tyxuKCQkGpm/rl8BihiZhBCS0niVAWBXmixQF+b/1N3gfDwTr69KbWFKjPPMgESeAXxjuq8OSMUd9fEhXnYfkT5DHPUz6SHgJx/JmfoHcJ9dNtnwG5vAhHuwy4Lpyppz/gIYWSPOpa854jCK05twUllIiPwTR0GpVOT65ylpBcjgodGIEpbN0bYgJkmiedvcCsC102VPwIDAQAB",
    oauth2: {
      client_id: OAUTH_CONFIG.clientId,
      scopes: OAUTH_CONFIG.scopes
    },
    action: {
      default_popup: "popup/index.html"
    },
    background: {
      service_worker: "background/index.js",
      type: "module"
    },
    web_accessible_resources: [
      {
        resources: ["popup/*", "icon/*"],
        matches: ["<all_urls>"]
      }
    ],
    content_security_policy: {
      extension_pages: "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"
    },
    host_permissions: [
      "https://agentcircle.ai/*",
      "https://chat.openai.com/*",
      "https://gemini.google.com/*",
      "https://claude.ai/*",
      "https://www.promptcircle.ai/*",
      "https://promptcircle.ai/*"
    ],
  },
  webExt: {
    startUrls: ["https://chatgpt.com"],
  },
});
