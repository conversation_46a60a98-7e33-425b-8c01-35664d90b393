// Inline-like Prompt Optimizer Loader
// This script can be easily integrated into existing content scripts

import './inline-integration';

// Simple initialization function
function initializeInlineOptimizer() {
  console.log('✨ Inline Optimizer: Initializing...');
  
  // Check if already initialized
  if (window.inlineOptimizerInitialized) {
    console.log('✨ Inline Optimizer: Already initialized');
    return;
  }

  try {
    // Import and initialize the optimizer
    import('./inline-integration').then(({ inlineOptimizer }) => {
      inlineOptimizer.initialize();
      window.inlineOptimizerInitialized = true;
      console.log('✨ Inline Optimizer: Successfully initialized');
    }).catch((error) => {
      console.error('✨ Inline Optimizer: Failed to initialize', error);
    });
  } catch (error) {
    console.error('✨ Inline Optimizer: Initialization error', error);
  }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeInlineOptimizer);
} else {
  initializeInlineOptimizer();
}

// Also initialize on navigation (for SPA support)
let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl) {
    lastUrl = url;
    // Reset initialization flag for new pages
    window.inlineOptimizerInitialized = false;
    setTimeout(initializeInlineOptimizer, 1000);
  }
}).observe(document, { subtree: true, childList: true });

// Export for manual initialization
export { initializeInlineOptimizer };

// Add to window for debugging
declare global {
  interface Window {
    inlineOptimizerInitialized?: boolean;
    initializeInlineOptimizer?: typeof initializeInlineOptimizer;
  }
}

window.initializeInlineOptimizer = initializeInlineOptimizer; 