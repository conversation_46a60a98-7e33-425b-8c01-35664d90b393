import React from 'react';
import { createRoot } from 'react-dom/client';
import { CHATBOTS_CONFIG, ChatbotConfig } from '@/utils/chatbotHandlers';
import InlineOptimizer from './inline-optimizer';

interface InlineIntegration {
  inputField: HTMLElement;
  container: HTMLElement;
  observer?: MutationObserver;
  isInitialized: boolean;
}

class InlinePromptOptimizer {
  private integrations: Map<string, InlineIntegration> = new Map();
  private isInitialized = false;
  private reinitTimeout: ReturnType<typeof setTimeout> | null = null;

  constructor() {
    this.init();
  }

  private init() {
    if (this.isInitialized) return;
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupOptimizer());
    } else {
      this.setupOptimizer();
    }

    // Setup observer for dynamic content
    this.setupObserver();
    
    this.isInitialized = true;
  }

  private setupOptimizer() {
    const currentUrl = window.location.href;
    
    // Find matching chatbot configuration
    const chatbotConfig = CHATBOTS_CONFIG.find(config => {
      if (config.urlCheck) {
        return config.urlCheck(currentUrl);
      }
      return true; // If no URL check, apply to all
    });

    if (!chatbotConfig) {
      console.log('Inline Optimizer: No matching chatbot configuration found');
      return;
    }

    // Find input fields
    const inputFields = this.findInputFields(chatbotConfig);
    
    inputFields.forEach((inputField, index) => {
      const integrationId = `${chatbotConfig.name}-${index}`;
      
      if (!this.integrations.has(integrationId)) {
        this.createIntegration(inputField, chatbotConfig, integrationId);
      }
    });
  }

  private findInputFields(chatbotConfig: ChatbotConfig): HTMLElement[] {
    const inputFields: HTMLElement[] = [];
    
    for (const selector of chatbotConfig.selectors) {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element instanceof HTMLElement) {
          inputFields.push(element);
        }
      });
    }
    
    return inputFields;
  }

  private createIntegration(
    inputField: HTMLElement, 
    chatbotConfig: ChatbotConfig, 
    integrationId: string
  ) {
    // Prevent duplicate optimizer for the same input field
    if (inputField.dataset.inlineOptimizerInjected === 'true') {
      return;
    }

    // Find or create container
    const container = this.findOrCreateContainer(inputField, chatbotConfig);
    
    if (!container) {
      console.error('Inline Optimizer: Could not create container for', integrationId);
      return;
    }

    // Create React root and render optimizer
    const root = document.createElement('div');
    root.id = `inline-optimizer-${integrationId}`;
    root.style.position = 'absolute';
    root.style.top = '50%';
    root.style.right = '12px';
    root.style.transform = 'translateY(-50%)';
    root.style.zIndex = '10';
    root.style.pointerEvents = 'auto';
    root.style.background = 'none';
    root.style.boxShadow = 'none';
    root.style.display = 'flex';
    root.style.alignItems = 'center';
    root.style.justifyContent = 'center';

    // Mark input as injected
    inputField.dataset.inlineOptimizerInjected = 'true';

    // Position root inside the input container
    container.style.position = 'relative';
    container.appendChild(root);

    // Render component
    const reactRoot = createRoot(root);
    reactRoot.render(
      React.createElement(InlineOptimizer, {
        inputField,
        onClose: () => this.removeIntegration(integrationId)
      })
    );

    // Store integration
    this.integrations.set(integrationId, {
      inputField,
      container,
      isInitialized: true
    });

    console.log(`Inline Optimizer: Initialized for ${chatbotConfig.name}`, integrationId);
  }

  private findOrCreateContainer(inputField: HTMLElement, chatbotConfig: ChatbotConfig): HTMLElement | null {
    // Try to find existing parent container
    const parentSelectors = Array.isArray(chatbotConfig.parentSelector) 
      ? chatbotConfig.parentSelector 
      : [chatbotConfig.parentSelector];

    for (const selector of parentSelectors) {
      const parent = inputField.closest(selector);
      if (parent instanceof HTMLElement) {
        return parent;
      }
    }

    // If no parent found, create a wrapper
    const wrapper = document.createElement('div');
    wrapper.className = `inline-wrapper ${chatbotConfig.buttonContainerClass}`;
    wrapper.style.position = 'relative';
    wrapper.style.display = 'inline-block';
    wrapper.style.width = '100%';
    
    // Insert wrapper before input field
    inputField.parentNode?.insertBefore(wrapper, inputField);
    wrapper.appendChild(inputField);
    
    return wrapper;
  }

  private setupObserver() {
    const observer = new MutationObserver((mutations) => {
      let shouldReinitialize = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              // Check if new input fields were added
              const hasNewInputs = CHATBOTS_CONFIG.some(config => {
                return config.selectors.some(selector => {
                  return node.querySelector(selector) || node.matches(selector);
                });
              });
              
              if (hasNewInputs) {
                shouldReinitialize = true;
              }
            }
          });
        }
      });
      
      if (shouldReinitialize) {
        // Debounce reinitialization
        if (this.reinitTimeout) {
          clearTimeout(this.reinitTimeout);
        }
        this.reinitTimeout = setTimeout(() => {
          this.setupOptimizer();
        }, 500);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private removeIntegration(integrationId: string) {
    const integration = this.integrations.get(integrationId);
    if (integration) {
      // Clean up React root
      const rootElement = document.getElementById(`inline-optimizer-${integrationId}`);
      if (rootElement) {
        rootElement.remove();
      }
      
      this.integrations.delete(integrationId);
      console.log(`Inline Optimizer: Removed integration ${integrationId}`);
    }
  }

  // Public method to manually initialize
  public initialize() {
    this.setupOptimizer();
  }

  // Public method to cleanup
  public cleanup() {
    this.integrations.forEach((_, integrationId) => {
      this.removeIntegration(integrationId);
    });
    this.integrations.clear();
  }
}

// Initialize the optimizer
const inlineOptimizer = new InlinePromptOptimizer();

// Export for potential external use
export { inlineOptimizer };
export default InlinePromptOptimizer; 