interface DomainConfig {
  pattern: string;
  anchor: string;
}

export const DOMAINS: DomainConfig[] = [
  {
    pattern: "*://chatgpt.com/*",
    anchor: "#thread-bottom"
  },
  {
    pattern: "*://chat.openai.com/*",
    anchor: "#thread-bottom"
  },
  {
    pattern: "*://gemini.google.com/*",
    anchor: "#thread-bottom"
  },
  {
    pattern: "*://claude.ai/*",
    anchor: "#thread-bottom"
  }
  // Add more domains here later
  // Example:
  // {
  //   pattern: "*://other-domain.com/*",
  //   anchor: "#other-anchor"
  // }
];

export const CONTENT_SCRIPT_MATCHES = DOMAINS.map(domain => domain.pattern);