import { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  FormControl, 
  FormControlLabel, 
  Radio, 
  RadioGroup, 
  Paper,
  Snackbar,
  IconButton,
  Divider,
  Chip,
  Avatar
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import PsychologyIcon from '@mui/icons-material/Psychology';
import { getDefaultLLM, setDefaultLLM, type LLMModel } from '@/utils/settings';

interface SettingsPanelProps {
  onClose: () => void;
}

const LLM_OPTIONS = [
  {
    value: 'ChatGPT' as const,
    label: 'ChatGPT',
    description: 'OpenAI\'s advanced language model',
    icon: <SmartToyIcon sx={{ fontSize: 20 }} />,
    color: '#10a37f'
  },
  {
    value: 'Gemini' as const,
    label: 'Gemini',
    description: 'Google\'s multimodal AI model',
    icon: <AutoAwesomeIcon sx={{ fontSize: 20 }} />,
    color: '#4285f4'
  },
  {
    value: 'Claude' as const,
    label: 'Claude',
    description: 'Anthropic\'s conversational AI',
    icon: <PsychologyIcon sx={{ fontSize: 20 }} />,
    color: '#d97706'
  }
];

export default function SettingsPanel({ onClose }: SettingsPanelProps) {
  const [selectedModel, setSelectedModel] = useState<LLMModel>('ChatGPT');
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState<string | null>(null);

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const defaultModel = await getDefaultLLM();
        setSelectedModel(defaultModel);
      } catch (error) {
        console.error('Failed to load settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Handle model selection change
  const handleModelChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newModel = event.target.value as LLMModel;
    setSelectedModel(newModel);

    try {
      await setDefaultLLM(newModel);
      setSuccess(`Default AI Model updated to ${newModel}`);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSuccess(`Failed to save settings. Please try again.`);
    }
  };

  if (loading) {
    return (
      <Box sx={{ 
        width: '400px', 
        minHeight: '600px',
        display: 'flex',
        flexDirection: 'column',
        background: '#fafafa',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'
      }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          flex: 1,
          color: '#666'
        }}>
          <Typography variant="h6">Loading settings...</Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      width: '400px', 
      minHeight: '600px',
      display: 'flex',
      flexDirection: 'column',
      background: '#fafafa',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'
    }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 48,
        px: 3,
        background: '#fff',
        borderBottom: '1px solid #e0e0e0',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton 
            onClick={onClose} 
            sx={{ 
              color: '#666',
              '&:hover': { 
                background: '#f5f5f5',
                transform: 'scale(1.05)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              background: '#e0904e',
              borderRadius: '50%',
              p: 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <SettingsIcon sx={{ color: 'white', fontSize: 20 }} />
            </Box>
            <Typography variant="h6" sx={{ 
              fontWeight: 600, 
              color: '#333', 
              fontSize: '1.25rem',
              letterSpacing: '-0.025em'
            }}>
              Settings
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ 
        flex: 1, 
        p: 1, 
        overflow: 'auto'
      }}>
        {/* Main Settings Card */}
        <Paper sx={{ 
          p: 3, 
          mb: 3,
          background: '#fff',
          borderRadius: 2,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0'
        }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ 
              mb: 1, 
              fontWeight: 600,
              color: '#333',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <Box sx={{
                background: '#e0904e',
                borderRadius: '50%',
                p: 0.5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <SmartToyIcon sx={{ color: 'white', fontSize: 18 }} />
              </Box>
              Default AI Model
            </Typography>
            <Typography variant="body2" sx={{ 
              color: '#666',
              lineHeight: 1.5
            }}>
              Choose your preferred AI model for prompt optimization and interactions
            </Typography>
          </Box>
          
          <FormControl component="fieldset" sx={{ width: '100%' }}>
            <RadioGroup
              value={selectedModel}
              onChange={handleModelChange}
            >
              {LLM_OPTIONS.map((option) => (
                <Box key={option.value} sx={{ mb: 2 }}>
                  <FormControlLabel 
                    value={option.value} 
                    control={
                      <Radio 
                        sx={{
                          color: option.color,
                          '&.Mui-checked': {
                            color: option.color,
                          },
                          '&:hover': {
                            background: `${option.color}10`
                          }
                        }}
                      />
                    } 
                    label={
                      <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 2,
                        p: 1.5,
                        borderRadius: 2,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          background: `${option.color}08`
                        },
                        width: '100%'
                      }}>
                        <Avatar sx={{ 
                          bgcolor: `${option.color}20`, 
                          color: option.color,
                          width: 32,
                          height: 32
                        }}>
                          {option.icon}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle1" sx={{ 
                            fontWeight: 600,
                            color: '#333',
                            mb: 0.5
                          }}>
                            {option.label}
                          </Typography>
                          <Typography variant="body2" sx={{ 
                            color: '#666',
                            fontSize: '0.875rem'
                          }}>
                            {option.description}
                          </Typography>
                        </Box>
                        {/* Reserve space for active chip to prevent layout shift */}
                        <Box sx={{ 
                          width: 60, 
                          height: 24, 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center' 
                        }}>
                          {selectedModel === option.value && (
                            <Chip 
                              label="Active" 
                              size="small" 
                              sx={{ 
                                bgcolor: `${option.color}20`,
                                color: option.color,
                                fontWeight: 600,
                                fontSize: '0.75rem',
                                height: 24,
                                '& .MuiChip-label': {
                                  px: 1
                                }
                              }} 
                            />
                          )}
                        </Box>
                      </Box>
                    }
                    sx={{ 
                      width: '100%',
                      margin: 0,
                      '& .MuiFormControlLabel-label': {
                        width: '100%'
                      }
                    }}
                  />
                </Box>
              ))}
            </RadioGroup>
          </FormControl>
          
          <Divider sx={{ my: 3 }} />
          
          <Box sx={{
            background: '#f8f9fa',
            p: 2,
            borderRadius: 2,
            border: '1px solid #e9ecef'
          }}>
            <Typography variant="body2" sx={{ 
              color: '#495057',
              fontSize: '0.875rem',
              lineHeight: 1.6,
              display: 'flex',
              alignItems: 'flex-start',
              gap: 1
            }}>
              <Box sx={{
                color: '#e0904e',
                fontSize: '1rem',
                mt: 0.1
              }}>
                ℹ️
              </Box>
              <span>
                <strong>Note:</strong> Your default AI model preference is synced across all your devices and affects prompt optimization and AI interactions throughout the extension.
              </span>
            </Typography>
          </Box>
        </Paper>

        {/* Info Card */}
        <Paper sx={{ 
          p: 3,
          background: '#fff',
          borderRadius: 2,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0'
        }}>
          <Typography variant="h6" sx={{ 
            mb: 2, 
            fontWeight: 600,
            color: '#333',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <Box sx={{
              background: '#28a745',
              borderRadius: '50%',
              p: 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <AutoAwesomeIcon sx={{ color: 'white', fontSize: 18 }} />
            </Box>
            About Settings
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
              <Box sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                bgcolor: '#28a745',
                mt: 1
              }} />
              <Typography variant="body2" sx={{ color: '#495057', lineHeight: 1.6 }}>
                Your preferences are automatically synced across all devices when signed into Chrome
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
              <Box sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                bgcolor: '#28a745',
                mt: 1
              }} />
              <Typography variant="body2" sx={{ color: '#495057', lineHeight: 1.6 }}>
                This setting affects prompt optimization and AI interactions throughout the extension
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
              <Box sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                bgcolor: '#28a745',
                mt: 1
              }} />
              <Typography variant="body2" sx={{ color: '#495057', lineHeight: 1.6 }}>
                You can change this setting at any time and it will be applied immediately
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Success Snackbar */}
      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
        message={success}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{
          '& .MuiSnackbarContent-root': {
            background: '#28a745',
            borderRadius: 2,
            fontWeight: 500
          }
        }}
      />
    </Box>
  );
} 