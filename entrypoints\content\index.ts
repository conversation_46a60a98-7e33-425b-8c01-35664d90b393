import './style.css';
import './bookmark.css';
import './inline-optimize/inline-loader';
import { CHATBOTS_CONFIG, ChatbotConfig, getCurrentChatbot, getMessages } from '@/utils/chatbotHandlers';
import { getBookmarks, addBookmark, removeBookmark } from '../../utils/bookmark';
import { assignPerfectButtonListener } from '../../utils/perfectPrompt';
import { BookmarkDialog } from './bookmark-dialog';

// Set the extension icon URL - ensure we set this as early as possible
function setExtensionIconUrl() {
  try {
    const iconUrl = chrome.runtime.getURL('/icon/48.png');
    document.documentElement.style.setProperty(
      '--extension-icon-url',
      `url("${iconUrl}")`
    );

    // Also set it on body as a fallback
    if (document.body) {
      document.body.style.setProperty(
        '--extension-icon-url',
        `url("${iconUrl}")`
      );
    }
  } catch (error) {
    console.error('Failed to set extension icon URL:', error);
  }
}

// Call immediately
setExtensionIconUrl();

// Also call when DOM is ready to ensure it works
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setExtensionIconUrl);
} else {
  setExtensionIconUrl();
}

interface ButtonContainer extends HTMLDivElement {
  observerRef?: MutationObserver;
  resizeListener?: () => void;
  scrollListener?: () => void;
  lastInput?: HTMLElement;
}

declare global {
  interface Window {
    promptPerfectInitialized?: boolean;
  }
}

function isExtensionValid(): boolean {
  try {
    return Boolean(chrome.runtime?.id);
  } catch {
    return false;
  }
}

function debounce<T extends (...args: any[]) => void>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | undefined;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function findChatbotInput(chatbot: ChatbotConfig): HTMLElement | null {
  for (const selector of chatbot.selectors) {
    const element = document.querySelector(selector);
    if (element instanceof HTMLElement) {
      return element;
    }
  }
  return null;
}

function findParentElement(chatbot: ChatbotConfig): HTMLElement | null {
  const selectors = Array.isArray(chatbot.parentSelector)
    ? chatbot.parentSelector
    : [chatbot.parentSelector];

  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element instanceof HTMLElement) {
      return element;
    }
  }
  return null;
}

function createButton(text: string, className: string, chatbot: ChatbotConfig): HTMLButtonElement {
  const button = document.createElement('button');
  button.innerText = text;
  button.classList.add(className);

  if (chatbot.customButtonContainer) {
    // button.style.cssText = chatbot.buttonStyle;
    button.addEventListener('mouseover', () => {
      if (!button.disabled) {
        button.style.cssText += chatbot.buttonHoverStyle;
      }
    });
    button.addEventListener('mouseout', () => {
      //   button.style.cssText = chatbot.buttonStyle;
    });
  }

  return button;
}

// Add global variable for table content UI
let globalTableContentUI: HTMLElement | null = null;

function createCollapsibleSection(title: string, className: string): HTMLElement {
  const section = document.createElement('div');
  section.className = `collapsible-section ${className}`;

  const header = document.createElement('div');
  header.className = 'collapsible-header';

  // Title span
  const titleSpan = document.createElement('span');
  titleSpan.className = 'collapsible-title';
  titleSpan.textContent = title;

  // Collapse icon
  const icon = document.createElement('span');
  icon.className = 'collapsible-icon';
  icon.textContent = '▼';

  header.appendChild(titleSpan);
  header.appendChild(icon);

  const content = document.createElement('div');
  content.className = 'collapsible-content';

  // Add click handler for collapse/expand
  header.addEventListener('click', () => {
    const isExpanded = section.classList.contains('expanded');
    section.classList.toggle('expanded');
    const iconEl = header.querySelector('.collapsible-icon');
    if (iconEl) {
      iconEl.textContent = isExpanded ? '▼' : '▶';
    }
  });

  section.appendChild(header);
  section.appendChild(content);

  return section;
}

function createTableContentUI(): HTMLElement {
  const container = document.createElement('div');
  container.className = 'table-content-container hidden';

  const header = document.createElement('div');
  header.className = 'table-content-header';

  const title = document.createElement('h2');
  title.className = 'table-content-title';
  title.textContent = 'Prompt Pilot';

  const closeButton = document.createElement('button');
  closeButton.className = 'table-content-close';
  closeButton.textContent = '×';
  closeButton.onclick = () => {
    container.classList.add('hidden');
    const tableContentButton = document.querySelector('.table-content-button');
    if (tableContentButton) {
      tableContentButton.classList.remove('hidden');
    }
  };

  header.appendChild(title);
  header.appendChild(closeButton);

  // Create sections container
  const sectionsContainer = document.createElement('div');
  sectionsContainer.className = 'table-content-sections';

  // Create Bookmark section
  const bookmarkSection = createCollapsibleSection('Bookmarks', 'bookmark-section');
  const bookmarkContent = document.createElement('div');
  bookmarkContent.className = 'bookmark-content';

  // Create Table Content section
  const tableContentSection = createCollapsibleSection('Table Content', 'table-content-section');
  const tableContentContent = document.createElement('div');
  tableContentContent.className = 'table-content-content';
  tableContentSection.querySelector('.collapsible-content')?.appendChild(tableContentContent);

  // Add empty state to bookmark section
  const bookmarkEmptyState = document.createElement('div');
  bookmarkEmptyState.className = 'bookmark-empty-state';
  bookmarkEmptyState.textContent = 'No content yet. Your messages will appear here.';
  bookmarkContent.appendChild(bookmarkEmptyState);

  bookmarkSection.querySelector('.collapsible-content')?.appendChild(bookmarkContent);

  // Add sections to container
  sectionsContainer.appendChild(bookmarkSection);
  sectionsContainer.appendChild(tableContentSection);

  container.appendChild(header);
  container.appendChild(sectionsContainer);

  return container;
}

function updateBookmarkContent(container: HTMLElement) {
  const content = container.querySelector('.bookmark-content');
  if (!content) return;

  // Clear existing content
  content.innerHTML = '';

  // Get current chatbot config
  const currentChatbot = getCurrentChatbot();
  if (!currentChatbot) {
    return;
  }

  // Get all bookmarks
  getBookmarks().then(bookmarks => {
    // If no bookmarks found, show empty state
    if (bookmarks.length === 0) {
      const emptyState = document.createElement('div');
      emptyState.className = 'bookmark-empty-state';
      emptyState.textContent = 'No bookmarks yet. Click the star icon to bookmark messages.';
      content.appendChild(emptyState);
      return;
    }

    // Add each bookmark
    bookmarks.forEach(bookmark => {
      const bookmarkItem = document.createElement('div');
      bookmarkItem.className = 'bookmark-item';
      
      // Create bookmark content with tags and notes
      const bookmarkContent = document.createElement('div');
      bookmarkContent.className = 'bookmark-content-wrapper';
      
      // Title
      const titleText = document.createElement('div');
      titleText.className = 'bookmark-title';
      titleText.textContent = bookmark.title || 'Untitled';
      
      // Main content
      const contentText = document.createElement('div');
      contentText.className = 'bookmark-text';
      contentText.textContent = bookmark.content;
      
      // Tags
      const tagsContainer = document.createElement('div');
      tagsContainer.className = 'bookmark-tags';
      if (bookmark.tags && bookmark.tags.length > 0) {
        bookmark.tags.forEach(tag => {
          const tagElement = document.createElement('span');
          tagElement.className = 'bookmark-tag';
          tagElement.textContent = `#${tag}`;
          tagsContainer.appendChild(tagElement);
        });
      }
      
      // Note
      const noteContainer = document.createElement('div');
      noteContainer.className = 'bookmark-note';
      if (bookmark.note) {
        noteContainer.textContent = bookmark.note;
      }
      
      // Source and date
      const metaContainer = document.createElement('div');
      metaContainer.className = 'bookmark-meta';
      const date = new Date(bookmark.timestamp).toLocaleDateString();
      metaContainer.textContent = `${bookmark.source} • ${date}`;
      
      // Assemble the bookmark item
      bookmarkContent.appendChild(titleText);
      bookmarkContent.appendChild(contentText);
      if (bookmark.tags && bookmark.tags.length > 0) {
        bookmarkContent.appendChild(tagsContainer);
      }
      if (bookmark.note) {
        bookmarkContent.appendChild(noteContainer);
      }
      bookmarkContent.appendChild(metaContainer);
      
      bookmarkItem.appendChild(bookmarkContent);
      
      // Add remove button
      const removeButton = document.createElement('button');
      removeButton.className = 'bookmark-remove';
      removeButton.title = 'Remove bookmark';
      removeButton.textContent = '×';
      bookmarkItem.appendChild(removeButton);

      // Add click handler for remove button
      removeButton.addEventListener('click', async (e) => {
        e.stopPropagation();
        if (confirm('Are you sure you want to remove this bookmark?')) {
          // Find and update the star button before removing the bookmark
          const node = document.querySelector(bookmark.nodeSelector);
          if (node) {
            const starBtn = node.querySelector('.bookmark-button');
            if (starBtn) {
              starBtn.classList.remove('active');
            }
          }
          
          // Remove the bookmark
          await removeBookmark(bookmark.id);
          
          // Update the bookmark content
          updateBookmarkContent(container);
        }
      });

      // Add click handler to scroll to message
      bookmarkItem.addEventListener('click', () => {
        scrollToBookmark(bookmark);
      });

      content.appendChild(bookmarkItem);
    });
  });
}

// Update isBookmarked function to use nodeSelector
async function isBookmarked(content: string, node: Element): Promise<boolean> {
  const bookmarks = await getBookmarks();
  // First try to match by nodeSelector if the node has a data-message-id
  const messageId = node.getAttribute('data-message-id');
  if (messageId) {
    return bookmarks.some(b => b.nodeSelector === `[data-message-id="${messageId}"]`);
  }
  // Fallback to content matching
  return bookmarks.some(b => b.content === content);
}

function isResponseStreaming(message: Element) {
  return (
    message.querySelector('.streaming-animation') ||
    document.querySelector('button[aria-label*="Stop streaming"]')
  );

  // Only check for .streaming-animation inside the message
  return !!message.querySelector('.streaming-animation');
}

function addBookmarkButton(message: Element) {
  // Get content based on current chatbot config
  const currentChatbot = getCurrentChatbot();
  if (!currentChatbot) return;

  // Check if this is an assistant message
  const isAssistantMessage = message.matches(currentChatbot.messageSelectors.assistantSelector);
  if (!isAssistantMessage) return;

  // Find the actual message container - it might be a parent element
  let messageContainer = message;
  const contentWrapper = message.querySelector(currentChatbot.messageSelectors.assistantContentWrapper || '');
  if (contentWrapper) {
    messageContainer = contentWrapper.parentElement || message;
  }
  // Add message-container class for hover effects
  messageContainer.classList.add('message-container');

  const messageContent = getMessageContent(messageContainer, false, currentChatbot);
  // const {assistantMessages: messageContent} = getMessages(currentChatbot);
  if (!messageContent) {
    return;
  }

  // Check if bookmark button already exists
  if (messageContainer.querySelector('.bookmark-button')) {
    return;
  }

  // Create bookmark button container
  const bookmarkContainer = document.createElement('div');
  bookmarkContainer.className = 'bookmark-button-container';

  const bookmarkButton = document.createElement('button');
  bookmarkButton.className = 'bookmark-button';
  bookmarkButton.title = 'Click to save this message';

  // Set innerHTML for dual icon (save + extension logo)
  bookmarkButton.innerHTML = `
    <span class="save-icon">💾</span>
    <img class="ext-logo-icon" src="${chrome.runtime.getURL('icon/32.png')}" alt="logo" />
  `;

  // Check if message is already bookmarked
  isBookmarked(messageContent, messageContainer).then(bookmarked => {
    if (bookmarked) {
      bookmarkButton.classList.add('active');
    }
  });

  bookmarkButton.addEventListener('click', async (e) => {
    e.stopPropagation();
    const isCurrentlyBookmarked = await isBookmarked(messageContent, messageContainer);

    if (isCurrentlyBookmarked) {
      const bookmarks = await getBookmarks();
      // Find bookmark by nodeSelector or content
      const messageId = messageContainer.getAttribute('data-message-id');
      const bookmark = messageId 
        ? bookmarks.find(b => b.nodeSelector === `[data-message-id="${messageId}"]`)
        : bookmarks.find(b => b.content === messageContent);

      if (bookmark) {
        await removeBookmark(bookmark.id);
        bookmarkButton.classList.remove('active');
      }
    } else {
      // Show bookmark dialog for adding tags and notes
      const dialog = new BookmarkDialog({
        content: messageContent,
        node: messageContainer,
        onSave: () => {
          bookmarkButton.classList.add('active');
        }
      });
      dialog.show().catch(error => {
        console.error('Error showing bookmark dialog:', error);
      });
    }

    // Update bookmark section if it exists
    const bookmarkSection = document.querySelector('.bookmark-section');
    if (bookmarkSection) {
      updateBookmarkContent(bookmarkSection as HTMLElement);
    }
  });

  bookmarkContainer.appendChild(bookmarkButton);
  messageContainer.appendChild(bookmarkContainer);
}

function updateTableContentContent(container: HTMLElement) {
  const content = container.querySelector('.table-content-content');
  if (!content) return;

  // Clear existing content
  content.innerHTML = '';

  // Get current chatbot config
  const currentChatbot = getCurrentChatbot();
  if (!currentChatbot) {
    const emptyState = document.createElement('div');
    emptyState.className = 'table-content-empty-state';
    emptyState.textContent = 'No conversation found. Start chatting to see your messages here.';
    content.appendChild(emptyState);
    return;
  }

  // Get messages using getMessages helper
  const { userMessages } = getMessages(currentChatbot);
  if (!userMessages || userMessages.length === 0) {
    const emptyState = document.createElement('div');
    emptyState.className = 'table-content-empty-state';
    emptyState.textContent = 'No messages yet. Start chatting to see your messages here.';
    content.appendChild(emptyState);
    return;
  }

  // Convert to array and process each message
  const messages = Array.from(userMessages).map((message, index) => {
    const messageContent = getMessageContent(message, true, currentChatbot);
    if (!messageContent) {
      return null;
    }

    // Create table content item
    const tableContentItem = document.createElement('div');
    tableContentItem.className = 'table-content-item';
    tableContentItem.setAttribute('data-index', (index + 1).toString());

    // Create index number
    const indexNumber = document.createElement('span');
    indexNumber.className = 'table-content-index';
    indexNumber.textContent = `${index + 1}.`;
    tableContentItem.appendChild(indexNumber);

    // Create message preview
    const messagePreview = document.createElement('span');
    messagePreview.className = 'table-content-preview';
    // Get first line or first 50 characters
    const firstLine = messageContent.split('\n')[0];
    const preview = firstLine.length > 50 ? firstLine.slice(0, 47) + '...' : firstLine;
    messagePreview.textContent = preview;
    tableContentItem.appendChild(messagePreview);

    // Add click handler to scroll to message
    tableContentItem.onclick = () => {
      // First, try to find the message's parent container that's actually visible
      let targetElement = message as HTMLElement;
      while (targetElement && (targetElement as HTMLElement).offsetParent === null && targetElement !== document.body) {
        targetElement = targetElement.parentElement as HTMLElement;
      }

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add highlight effect
        const targetHtmlElement = targetElement as HTMLElement;
        const originalBackground = targetHtmlElement.style.background;
        const originalTransition = targetHtmlElement.style.transition;
        targetHtmlElement.style.transition = 'all 0.3s ease';
        targetHtmlElement.style.background = 'rgba(74, 144, 226, 0.1)';
        targetHtmlElement.style.borderRadius = '8px';
        targetHtmlElement.style.boxShadow = '0 0 0 2px rgba(74, 144, 226, 0.3)';

        // Remove highlight after animation
        setTimeout(() => {
          targetHtmlElement.style.background = originalBackground;
          targetHtmlElement.style.boxShadow = 'none';
          targetHtmlElement.style.borderRadius = '';
          targetHtmlElement.style.transition = originalTransition;
        }, 2000);
      }
    };

    return tableContentItem;
  }).filter(Boolean);

  // Add all messages to content
  messages.forEach(message => {
    if (message) content.appendChild(message);
  });

  // Expand the section after updating content
  const section = container.closest('.table-content-section');
  if (section && !section.classList.contains('expanded')) {
    section.classList.add('expanded');
    const iconEl = section.querySelector('.collapsible-icon');
    if (iconEl) {
      iconEl.textContent = '▶';
    }
  }
}

function assignTableContentButtonListener(button: HTMLButtonElement, inputField: HTMLElement) {
  button.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      button.classList.add('loading');

      // Use global table content UI
      if (globalTableContentUI) {
        // Update content
        updateTableContentContent(globalTableContentUI);
        // Show UI
        globalTableContentUI.classList.remove('hidden');
      }

      // Hide table content button
      button.classList.add('hidden');

    } catch (error) {
      console.error('Error in table content button handler:', error);
      alert('Error: Could not open table content. Please try again.');
    } finally {
      button.classList.remove('loading');
    }
  });
}

function addButtons(chatbot: ChatbotConfig, inputField: HTMLElement): void {
  if (!isExtensionValid()) {
    return;
  }
  
  if (inputField.dataset.promptPerfectInitialized === 'true') {
    return;
  }

  // Remove any existing button container
  const existingButtons = document.querySelector('.' + chatbot.buttonContainerClass) as ButtonContainer;
  if (existingButtons) {
    if (existingButtons.observerRef) {
      existingButtons.observerRef.disconnect();
    }
    if (existingButtons.resizeListener) {
      window.removeEventListener('resize', existingButtons.resizeListener);
    }
    if (existingButtons.scrollListener) {
      window.removeEventListener('scroll', existingButtons.scrollListener, true);
    }
    existingButtons.remove();
  }

  // Create button container
  const buttonContainer = document.createElement('div') as ButtonContainer;
  buttonContainer.classList.add(chatbot.buttonContainerClass, 'prompt-perfect-buttons-container');

  // Create and append buttons
  const perfectButton = createButton('Perfect', 'perfect-button', chatbot);
  const tableContentButton = createButton('Table Content', 'table-content-button', chatbot);

  // Assign listeners
  assignPerfectButtonListener(perfectButton, inputField);
  assignTableContentButtonListener(tableContentButton, inputField);

  // Disable perfect button for now
  // buttonContainer.appendChild(perfectButton);
  // buttonContainer.appendChild(tableContentButton);

  // Append to parent element
  const parentElement = findParentElement(chatbot);
  if (!parentElement) {
    return;
  }

  parentElement.insertBefore(buttonContainer, parentElement.firstChild);

  // Add submit handler for input field
  const handleSubmit = async () => {
    let attempts = 0;
    const maxAttempts = 20; // 10 seconds total with 500ms interval
    let messageProcessed = false;

    // Function to check loading state and process message
    const checkStateAndProcess = () => {
      attempts++;
      const currentChatbot = getCurrentChatbot();
      if (!currentChatbot || messageProcessed) return;

      const isLoading = isSubmitButtonLoading(currentChatbot);

      // Get current messages
      const { assistantMessages } = getMessages(currentChatbot);
      const lastMessage = assistantMessages[assistantMessages.length - 1];

      if (isLoading) {
        // Still loading, check again after delay
        if (attempts < maxAttempts) {
          setTimeout(checkStateAndProcess, 500);
        } else {
          console.log("Max attempts reached while waiting for response");
        }
        return;
      }

      // Loading finished, check if we have a valid message
      if (!lastMessage || 
          !lastMessage.matches(currentChatbot.messageSelectors.assistantSelector) || 
          lastMessage.querySelector('.bookmark-button')) {
        if (attempts < maxAttempts) {
          setTimeout(checkStateAndProcess, 500);
        } else {
          console.log("Max attempts reached waiting for valid message");
        }
        return;
      }

      // Check message content
      const messageContent = getMessageContent(lastMessage, false, currentChatbot);
      if (!messageContent || messageContent.trim() === '') {
        if (attempts < maxAttempts) {
          setTimeout(checkStateAndProcess, 500);
        } else {
          console.log("Max attempts reached waiting for message content");
        }
        return;
      }

      // Check paragraphs if wrapper exists
      const wrapper = lastMessage.querySelector(currentChatbot.messageSelectors.assistantContentWrapper || '');
      if (wrapper) {
        const paragraphs = wrapper.querySelectorAll(currentChatbot.messageSelectors.assistantSelectorText);
        if (paragraphs.length === 0) {
          if (attempts < maxAttempts) {
            setTimeout(checkStateAndProcess, 500);
          } else {
            console.log("Max attempts reached waiting for paragraphs");
          }
          return;
        }
      }

      // Wait for streaming to finish
      if (isResponseStreaming(lastMessage)) {
        setTimeout(checkStateAndProcess, 500);
        return;
      }

      // All checks passed, add bookmark button
      console.log("Message is ready, adding bookmark button");
      messageProcessed = true;

      // Add bookmark button to the new message
      setTimeout(() => {
        addBookmarkButton(lastMessage);
      }, 1000);

      // Update table content if it's visible
      if (globalTableContentUI && !globalTableContentUI.classList.contains('hidden')) {
        updateTableContentContent(globalTableContentUI);
      }

      // Update bookmark section if it exists and is visible
      const bookmarkSection = document.querySelector('.bookmark-section');
      if (bookmarkSection && !bookmarkSection.classList.contains('hidden')) {
        updateBookmarkContent(bookmarkSection as HTMLElement);
      }
    };

    // Start checking state
    checkStateAndProcess();
  };

  // Add submit event listener based on input type
  if (inputField instanceof HTMLFormElement) {
    inputField.addEventListener('submit', handleSubmit);
  } else {
    // For non-form elements, listen for Enter key
    inputField.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSubmit();
      }
    });
  }

  // Mark as initialized
  inputField.dataset.promptPerfectInitialized = 'true';

  // Send handshake message to background/popup
  try {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      url: window.location.href
    });
  } catch (e) {
    console.warn('Failed to send contentScriptReady handshake:', e);
  }
}

function initializeExtension(): void {
  const setupObserver = () => {
    if (!document.body) {
      setTimeout(setupObserver, 100);
      return;
    }

    // Create table content UI once during initialization
    if (!globalTableContentUI) {
      globalTableContentUI = createTableContentUI();
      document.body.appendChild(globalTableContentUI);
    }

    // Handle chatbots with observer logic
    CHATBOTS_CONFIG.forEach(chatbot => {
      // Check if URL matches chatbot's URL check
      if (chatbot.urlCheck && !chatbot.urlCheck(window.location.href)) {
        return;
      }
  
      const addButtonsIfNeeded = debounce(() => {
        const chatbotInput = findChatbotInput(chatbot);
        if (chatbotInput && !chatbotInput.dataset.promptPerfectInitialized) {
          addButtons(chatbot, chatbotInput);
          chatbotInput.dataset.promptPerfectInitialized = 'true';
        }
      }, 250);

      addButtonsIfNeeded();

      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            addButtonsIfNeeded();
            break;
          }
        }
      });

      observer.observe(document.body, {
        childList: true, 
        subtree: true 
      });
    });
  };

  setupObserver();
}

export default defineContentScript({
  matches: ['<all_urls>'],
  async main() {
    console.log('Prompt Perfect content script loaded on:', window.location.href);
    console.log('Content script initialization started');
    
    // Kiểm tra trạng thái đăng nhập trước khi init UI content
    const tryInitContentUI = async () => {
      // const authState = await getAuthState();
      // if (!authState.isAuthenticated) {
      //   console.log('User not authenticated, skip content UI init');
      //   return;
      // }
      if (!window.promptPerfectInitialized) {
        window.promptPerfectInitialized = true;
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initializeExtension);
        } else {
          initializeExtension();
        }
      }
    };
    await tryInitContentUI();
    
    console.log('Setting up message listener...');
    
    // Listen for custom promptReplaced event from inline optimizer
    document.addEventListener('promptReplaced', (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log('Prompt replaced event received:', customEvent.detail);
      
      // Trigger submit monitoring for the replaced prompt
      const triggerSubmitMonitoring = () => {
        console.log('Triggering submit monitoring for replaced prompt');
        const currentChatbot = getCurrentChatbot();
        if (currentChatbot) {
          let attempts = 0;
          const maxAttempts = 20;
          let messageProcessed = false;

          const checkStateAndProcess = () => {
            attempts++;
            if (!currentChatbot || messageProcessed) return;

            const isLoading = isSubmitButtonLoading(currentChatbot);
            const { assistantMessages } = getMessages(currentChatbot);
            const lastMessage = assistantMessages[assistantMessages.length - 1];

            if (isLoading) {
              if (attempts < maxAttempts) {
                setTimeout(checkStateAndProcess, 500);
              } else {
                console.log("Max attempts reached while waiting for response");
              }
              return;
            }

            if (!lastMessage || 
                !lastMessage.matches(currentChatbot.messageSelectors.assistantSelector) || 
                lastMessage.querySelector('.bookmark-button')) {
              if (attempts < maxAttempts) {
                setTimeout(checkStateAndProcess, 500);
              } else {
                console.log("Max attempts reached waiting for valid message");
              }
              return;
            }

            const messageContent = getMessageContent(lastMessage, false, currentChatbot);
            if (!messageContent || messageContent.trim() === '') {
              if (attempts < maxAttempts) {
                setTimeout(checkStateAndProcess, 500);
              } else {
                console.log("Max attempts reached waiting for message content");
              }
              return;
            }

            const wrapper = lastMessage.querySelector(currentChatbot.messageSelectors.assistantContentWrapper || '');
            if (wrapper) {
              const paragraphs = wrapper.querySelectorAll(currentChatbot.messageSelectors.assistantSelectorText);
              if (paragraphs.length === 0) {
                if (attempts < maxAttempts) {
                  setTimeout(checkStateAndProcess, 500);
                } else {
                  console.log("Max attempts reached waiting for paragraphs");
                }
                return;
              }
            }

            if (isResponseStreaming(lastMessage)) {
              setTimeout(checkStateAndProcess, 500);
              return;
            }

            console.log("Message is ready, adding bookmark button");
            messageProcessed = true;

            setTimeout(() => {
              addBookmarkButton(lastMessage);
            }, 1000);

            if (globalTableContentUI && !globalTableContentUI.classList.contains('hidden')) {
              updateTableContentContent(globalTableContentUI);
            }

            const bookmarkSection = document.querySelector('.bookmark-section');
            if (bookmarkSection && !bookmarkSection.classList.contains('hidden')) {
              updateBookmarkContent(bookmarkSection as HTMLElement);
            }
          };

          checkStateAndProcess();
        }
      };

      // Start monitoring after a short delay to allow the user to send the message
      setTimeout(triggerSubmitMonitoring, 1000);
    });
    
    // Lắng nghe message AUTH_STATE_CHANGED để re-check authState
    chrome.runtime.onMessage.addListener((message: { type: string; action?: string; prompt?: string; selector?: string }) => {
      console.log('Content script received message:', message);
      
      if (message.type === 'AUTH_STATE_CHANGED') {
        tryInitContentUI();
      }
      
      // Handle fillPrompt action
      if (message.action === 'fillPrompt' && message.prompt && message.selector) {
        console.log('Processing fillPrompt action:', { prompt: message.prompt, selector: message.selector });
        
        // Helper function to trigger submit monitoring (same logic as handleSubmit)
        const triggerSubmitMonitoring = () => {
          console.log('Triggering submit monitoring for filled prompt');
          const currentChatbot = getCurrentChatbot();
          if (currentChatbot) {
            let attempts = 0;
            const maxAttempts = 20;
            let messageProcessed = false;

            const checkStateAndProcess = () => {
              attempts++;
              if (!currentChatbot || messageProcessed) return;

              const isLoading = isSubmitButtonLoading(currentChatbot);
              const { assistantMessages } = getMessages(currentChatbot);
              const lastMessage = assistantMessages[assistantMessages.length - 1];

              if (isLoading) {
                if (attempts < maxAttempts) {
                  setTimeout(checkStateAndProcess, 500);
                } else {
                  console.log("Max attempts reached while waiting for response");
                }
                return;
              }

              if (!lastMessage || 
                  !lastMessage.matches(currentChatbot.messageSelectors.assistantSelector) || 
                  lastMessage.querySelector('.bookmark-button')) {
                if (attempts < maxAttempts) {
                  setTimeout(checkStateAndProcess, 500);
                } else {
                  console.log("Max attempts reached waiting for valid message");
                }
                return;
              }

              const messageContent = getMessageContent(lastMessage, false, currentChatbot);
              if (!messageContent || messageContent.trim() === '') {
                if (attempts < maxAttempts) {
                  setTimeout(checkStateAndProcess, 500);
                } else {
                  console.log("Max attempts reached waiting for message content");
                }
                return;
              }

              const wrapper = lastMessage.querySelector(currentChatbot.messageSelectors.assistantContentWrapper || '');
              if (wrapper) {
                const paragraphs = wrapper.querySelectorAll(currentChatbot.messageSelectors.assistantSelectorText);
                if (paragraphs.length === 0) {
                  if (attempts < maxAttempts) {
                    setTimeout(checkStateAndProcess, 500);
                  } else {
                    console.log("Max attempts reached waiting for paragraphs");
                  }
                  return;
                }
              }

              if (isResponseStreaming(lastMessage)) {
                setTimeout(checkStateAndProcess, 500);
                return;
              }

              console.log("Message is ready, adding bookmark button");
              messageProcessed = true;

              setTimeout(() => {
                addBookmarkButton(lastMessage);
              }, 1000);

              if (globalTableContentUI && !globalTableContentUI.classList.contains('hidden')) {
                updateTableContentContent(globalTableContentUI);
              }

              const bookmarkSection = document.querySelector('.bookmark-section');
              if (bookmarkSection && !bookmarkSection.classList.contains('hidden')) {
                updateBookmarkContent(bookmarkSection as HTMLElement);
              }
            };

            checkStateAndProcess();
          }
        };
        
        const fillPrompt = () => {
          console.log('Attempting to find element with selector:', message.selector);
          
          // Use the exact same logic that worked in the manual test
          const input = (document.querySelector('textarea[data-id="root"]') || 
                        document.querySelector('textarea[placeholder*="Message"]') ||
                        document.querySelector('[contenteditable="true"]')) as HTMLTextAreaElement | HTMLInputElement;
          
          if (input) {
            console.log('Found input element:', input);
            
            if (input.getAttribute('contenteditable') === 'true') {
              console.log('Filling contenteditable element');
              input.textContent = message.prompt!.replace(/"/g, '');
            } else {
              console.log('Filling regular input element');
              input.value = message.prompt!.replace(/"/g, '');
            }
            
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.focus();
            
            console.log('Prompt filled successfully:', message.prompt);
            return input;
          }
          console.log('No input found');
          return null;
        };

        // Try immediately
        let filledInput = fillPrompt();
        if (!filledInput) {
          console.log('First attempt failed, trying again after delay...');
          // If not found, wait a bit and try again
          setTimeout(() => {
            filledInput = fillPrompt();
            if (!filledInput) {
              console.log('Second attempt failed, trying fallback selectors...');
              // Try with a more generic selector as fallback
              const fallbackSelectors = [
                'textarea[placeholder*="Message"]',
                'textarea[placeholder*="Ask"]',
                'textarea[placeholder*="Type"]',
                'textarea[placeholder*="Send"]',
                'input[placeholder*="Message"]',
                'input[placeholder*="Ask"]',
                'input[placeholder*="Type"]',
                'input[placeholder*="Send"]',
                '.ql-editor',
                '[contenteditable="true"]',
                'div[role="textbox"]'
              ];
              
              for (const selector of fallbackSelectors) {
                console.log('Trying fallback selector:', selector);
                const element = document.querySelector(selector) as HTMLTextAreaElement | HTMLInputElement;
                if (element) {
                  console.log('Found element with fallback selector:', selector);
                  if (element.getAttribute('contenteditable') === 'true') {
                    element.textContent = message.prompt!;
                  } else {
                    element.value = message.prompt!;
                  }
                  element.dispatchEvent(new Event('input', { bubbles: true }));
                  element.dispatchEvent(new Event('change', { bubbles: true }));
                  element.focus();
                  console.log('Prompt filled with fallback selector:', selector);
                  filledInput = element;
                  break;
                }
              }
            }
            
            // After filling the prompt, trigger submit monitoring if input was found
            if (filledInput) {
              triggerSubmitMonitoring();
            }
          }, 1000);
        } else {
          // If input was found immediately, trigger submit monitoring
          triggerSubmitMonitoring();
        }
      }
    });
    
    console.log('Message listener setup complete');
    console.log('Content script initialization complete');
  }
});
