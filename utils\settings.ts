export type LLMModel = 'ChatGPT' | 'Gemini' | 'Claude';

const DEFAULT_LLM: LLMModel = 'ChatGPT';

/**
 * Get the default LLM setting from storage
 * @returns Promise<LLMModel> - The default LLM model
 */
export async function getDefaultLLM(): Promise<LLMModel> {
  try {
    // Try sync storage first (for cross-device sync)
    const result = await chrome.storage.sync.get(['defaultLLM']);
    if (result.defaultLLM && ['ChatGPT', 'Gemini', 'Claude'].includes(result.defaultLLM)) {
      return result.defaultLLM as LLMModel;
    }
  } catch (error) {
    console.log('Sync storage not available, trying local storage');
  }

  try {
    // Fallback to local storage
    const result = await chrome.storage.local.get(['defaultLLM']);
    if (result.defaultLLM && ['ChatGPT', 'Gemini', 'Claude'].includes(result.defaultLLM)) {
      return result.defaultLLM as LLMModel;
    }
  } catch (error) {
    console.error('Failed to get default LLM from storage:', error);
  }

  return DEFAULT_LLM;
}

/**
 * Set the default LLM setting in storage
 * @param model - The LLM model to set as default
 * @returns Promise<void>
 */
export async function setDefaultLLM(model: LLMModel): Promise<void> {
  try {
    // Save to sync storage first (for cross-device sync)
    await chrome.storage.sync.set({ defaultLLM: model });
    
    // Also save to local storage as fallback
    await chrome.storage.local.set({ defaultLLM: model });
  } catch (error) {
    console.error('Failed to save to sync storage, using local storage only:', error);
    try {
      await chrome.storage.local.set({ defaultLLM: model });
    } catch (localError) {
      console.error('Failed to save to local storage:', localError);
      throw localError;
    }
  }
}

/**
 * Get all available LLM models
 * @returns LLMModel[] - Array of available LLM models
 */
export function getAvailableLLMModels(): LLMModel[] {
  return ['ChatGPT', 'Gemini', 'Claude'];
} 