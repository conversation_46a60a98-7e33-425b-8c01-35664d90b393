import { useState, useMemo, useEffect } from 'react';
import {
  Box, Typography, Snackbar, TextField, Select, MenuItem, InputLabel, FormControl, Chip, Card, CardContent, IconButton, Tooltip, Modal, Divider, CircularProgress, <PERSON><PERSON>
} from '@mui/material';
import PromptSaveModal, { PromptData } from './PromptSaveModal';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import SaveIcon from '@mui/icons-material/Save';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import StarIcon from '@mui/icons-material/Star';
import LabelIcon from '@mui/icons-material/Label';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { promptCircleApi, CommunityPrompt, CommunityPromptsResponse } from '../../utils/promptCircleApi';

// Default tags and LLMs - will be updated from API data
const DEFAULT_TAGS = ['Copywriting', 'Blog', 'Coding', 'UI/UX', 'FB Ads', 'Content Creation', 'Marketing', 'YT Creator', 'Tiktok', 'StackOverflow'];
const DEFAULT_LLM = ['ChatGPT', 'Claude', 'Gemini'];
const SORT_OPTIONS = [
  { value: 'date', label: 'Date' },
  { value: 'upvotes', label: 'Upvotes' },
  { value: 'rating', label: 'Rating' },
  { value: 'featured', label: 'Featured' },
];
const FEATURED_OPTIONS = [
  { value: '', label: 'All' },
  { value: 'featured', label: 'Featured' },
  { value: 'not_featured', label: 'Not Featured' },
];

export default function CommunityPanel({ onUsePrompt, onSaveToLibrary }: { onUsePrompt: (prompt: string) => void; onSaveToLibrary: (title: string, content: string) => void }) {
  // API data state
  const [prompts, setPrompts] = useState<CommunityPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPrevPage, setHasPrevPage] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Filter/search state
  const [search, setSearch] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [selectedLLM, setSelectedLLM] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [featured, setFeatured] = useState('');
  
  // Snackbar
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });
  // Modals
  const [viewModal, setViewModal] = useState<{ open: boolean; prompt: CommunityPrompt | null }>({ open: false, prompt: null });
  const [submitModalOpen, setSubmitModalOpen] = useState(false);

  // Load community prompts from API
  const loadPrompts = async (page: number = 1) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await promptCircleApi.getCommunityPrompts(page);
      
      if ('error' in result) {
        setError(result.error);
        setSnackbar({ open: true, message: result.error, severity: 'error' });
      } else {
        // Ensure we always set an array, even if API returns null/undefined
        setPrompts(result.prompts || []);
        setTotalCount(result.count || 0);
        setHasNextPage(!!result.next);
        setHasPrevPage(!!result.previous);
        setCurrentPage(page);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prompts';
      setError(errorMessage);
      setSnackbar({ open: true, message: errorMessage, severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Load prompts on component mount
  useEffect(() => {
    loadPrompts(1);
  }, []);

  // Extract unique tags and LLMs from API data
  const allTags = useMemo(() => {
    if (!prompts || !Array.isArray(prompts)) return DEFAULT_TAGS;
    const tags = prompts.flatMap(p => {
      if (!p || !p.tags) return [];
      return p.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    });
    return Array.from(new Set([...DEFAULT_TAGS, ...tags]));
  }, [prompts]);

  const allLLMs = useMemo(() => {
    if (!prompts || !Array.isArray(prompts)) return DEFAULT_LLM;
    const llms = prompts.map(p => p?.compatible_llms).filter(llm => llm);
    return Array.from(new Set([...DEFAULT_LLM, ...llms]));
  }, [prompts]);

  // Filtered and sorted prompts
  const filteredPrompts = useMemo(() => {
    if (!prompts || !Array.isArray(prompts)) return [];
    
    let filtered = prompts.filter(p => {
      if (!p) return false;
      
      const matchesSearch =
        (p.title?.toLowerCase() || '').includes(search.toLowerCase()) ||
        (p.content?.toLowerCase() || '').includes(search.toLowerCase()) ||
        (p.description?.toLowerCase() || '').includes(search.toLowerCase()) ||
        (p.tags?.toLowerCase() || '').includes(search.toLowerCase());
      const matchesTag = selectedTag ? (p.tags?.toLowerCase() || '').includes(selectedTag.toLowerCase()) : true;
      const matchesLLM = selectedLLM ? (p.compatible_llms?.toLowerCase() || '').includes(selectedLLM.toLowerCase()) : true;
      const matchesFeatured =
        featured === 'featured' ? p.is_featured :
        featured === 'not_featured' ? !p.is_featured : true;
      return matchesSearch && matchesTag && matchesLLM && matchesFeatured;
    });

    // Sort
    if (sortBy === 'date') {
      filtered = filtered.slice().sort((a, b) => {
        const dateA = a?.created_at ? new Date(a.created_at).getTime() : 0;
        const dateB = b?.created_at ? new Date(b.created_at).getTime() : 0;
        return dateB - dateA;
      });
    } else if (sortBy === 'upvotes') {
      filtered = filtered.slice().sort((a, b) => (b?.upvotes || 0) - (a?.upvotes || 0));
    } else if (sortBy === 'rating') {
      filtered = filtered.slice().sort((a, b) => (b?.rating_score || 0) - (a?.rating_score || 0));
    } else if (sortBy === 'featured') {
      filtered = filtered.slice().sort((a, b) => (b?.is_featured ? 1 : 0) - (a?.is_featured ? 1 : 0));
    }
    
    return filtered;
  }, [prompts, search, selectedTag, selectedLLM, sortBy, featured]);

  // Actions
  const handleUse = async (promptContent: string) => {
    if (!promptContent.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }

    let url = '';
    let selector = '';

    switch (selectedLLM) {
      case 'ChatGPT':
        url = 'https://chat.openai.com/';
        selector = 'textarea[data-id="root"], textarea[placeholder*="Message"], textarea[placeholder*="Send a message"]';
        break;
      case 'Gemini':
        url = 'https://gemini.google.com/app';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Claude':
        url = 'https://claude.ai/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      default:
        setSnackbar({ open: true, message: 'Invalid LLM selection.', severity: 'error' });
        return;
    }

    setSnackbar({ open: true, message: `Opening ${selectedLLM}...`, severity: 'success' });

    chrome.runtime.sendMessage({
      action: 'openAndFillPrompt',
      url,
      prompt: promptContent,
      selector
    }, (response) => {
      if (response && response.success) {
        setSnackbar({ open: true, message: `Prompt filled in ${selectedLLM}!`, severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response && response.error ? response.error : 'Failed to fill prompt.', severity: 'error' });
      }
    });
  };
  const handleSave = (title: string, content: string) => {
    onSaveToLibrary(title, content);
    setSnackbar({ open: true, message: 'Prompt saved to Library!', severity: 'success' });
  };
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    setSnackbar({ open: true, message: 'Prompt copied!', severity: 'success' });
  };
  const handleView = (prompt: any) => {
    setViewModal({ open: true, prompt });
  };
  const handleCloseView = () => setViewModal({ open: false, prompt: null });

  // Submit modal
  const handleOpenSubmit = () => setSubmitModalOpen(true);
  const handleCloseSubmit = () => setSubmitModalOpen(false);
  const handleSubmitPrompt = (data: PromptData) => {
    setSubmitModalOpen(false);
    setSnackbar({ open: true, message: 'Prompt submitted!', severity: 'success' });
  };

  return (
    <Box sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      {/* Search & Filters */}
      <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
        <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>🔍 Search & Filters</Typography>
        <TextField
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder="Search prompts by keyword..."
          size="small"
          fullWidth
          InputProps={{
            startAdornment: <span role="img" aria-label="search">🔎</span>,
            style: { borderRadius: 8 }
          }}
          sx={{ mb: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={e => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {allTags.map((tag: string) => (
                <MenuItem key={tag} value={tag}>{tag}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🤖 LLM</InputLabel>
            <Select
              value={selectedLLM}
              label="🤖 LLM"
              onChange={e => setSelectedLLM(e.target.value)}
            >
              <MenuItem value="">All LLMs</MenuItem>
              {allLLMs.map((llm: string) => (
                <MenuItem key={llm} value={llm}>{llm}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>📅 Sort</InputLabel>
            <Select
              value={sortBy}
              label="📅 Sort"
              onChange={e => setSortBy(e.target.value)}
            >
              {SORT_OPTIONS.map(opt => (
                <MenuItem key={opt.value} value={opt.value}>{opt.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏅 Featured</InputLabel>
            <Select
              value={featured}
              label="🏅 Featured"
              onChange={e => setFeatured(e.target.value)}
            >
              {FEATURED_OPTIONS.map(opt => (
                <MenuItem key={opt.value} value={opt.value}>{opt.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Prompt Cards List */}
      <Box sx={{ flex: 1, overflowY: 'auto', background: '#f9fafb', p: 2, display: 'flex', flexDirection: 'column', gap: 2, minHeight: 0 }}>
        {filteredPrompts.length === 0 ? (
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>No prompts found.</Typography>
        ) : (
          <>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Loading community prompts...</Typography>
              </Box>
            ) : error ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography color="error" sx={{ mb: 2 }}>{error}</Typography>
                <Button variant="contained" onClick={() => loadPrompts(1)}>Retry</Button>
              </Box>
            ) : (
              <>
                {filteredPrompts.map(item => (
                  <Card key={item.id} sx={{ borderRadius: 2, border: '1px solid #e0e0e0', boxShadow: 0, p: 0, position: 'relative', overflow: 'visible' }}>
                    {item.is_featured && (
                      <Box sx={{ position: 'absolute', top: 8, right: 8, bgcolor: '#fef08a', color: '#b45309', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                        <StarIcon fontSize="inherit" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} /> FEATURED
                      </Box>
                    )}
                    <CardContent sx={{ p: 2, pb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <span role="img" aria-label="brain" style={{ fontSize: 20 }}>🧠</span>
                        <Typography fontWeight={700} fontSize={15} sx={{ flex: 1 }}>{item.title}</Typography>
                      </Box>
                      <Typography fontSize={13} color="text.secondary" sx={{ mb: 1, whiteSpace: 'pre-line' }}>{item.description || (item.content.length > 120 ? item.content.slice(0, 120) + '...' : item.content)}</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 1 }}>
                        {item.tags.split(',').map((tag: string) => (
                          <Chip key={tag.trim()} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag.trim()} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                        ))}
                        <Chip icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={item.compatible_llms} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                        <span>👍 {item.upvotes}</span>
                        <span>👎 {item.downvotes}</span>
                        <span>⭐ {item.rating_score.toFixed(1)}</span>
                        <span>📅 {new Date(item.created_at).toLocaleDateString()}</span>
                        {item.is_featured && <span>🏅 Featured</span>}
                        <span style={{ marginLeft: 'auto', color: '#2563eb', fontWeight: 500 }}>👤 {item.user}</span>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
                        <Tooltip title="Use Prompt"><IconButton color="primary" size="small" onClick={() => handleUse(item.content)}><RocketLaunchIcon /></IconButton></Tooltip>
                        <Tooltip title="Save to Library"><IconButton size="small" onClick={() => handleSave(item.title, item.content)}><SaveIcon /></IconButton></Tooltip>
                        <Tooltip title="Copy"><IconButton size="small" onClick={() => handleCopy(item.content)}><ContentCopyIcon /></IconButton></Tooltip>
                        <Tooltip title="View Details"><IconButton size="small" onClick={() => handleView(item)}><VisibilityIcon /></IconButton></Tooltip>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
                
                {/* Pagination */}
                {(hasNextPage || hasPrevPage) && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
                    <Button 
                      variant="outlined" 
                      disabled={!hasPrevPage || loading}
                      onClick={() => loadPrompts(currentPage - 1)}
                    >
                      Previous
                    </Button>
                    <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                      Page {currentPage} of {Math.ceil(totalCount / 10)}
                    </Typography>
                    <Button 
                      variant="outlined" 
                      disabled={!hasNextPage || loading}
                      onClick={() => loadPrompts(currentPage + 1)}
                    >
                      Next
                    </Button>
                  </Box>
                )}
              </>
            )}
          </>
        )}
      </Box>

      {/* Submit Your Prompt */}
      {/* <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', background: '#fff' }}>
        <button style={{ width: '100%', fontWeight: 700, borderRadius: 8, padding: '10px 0', background: '#22c55e', color: '#fff', border: 'none', fontSize: 16, cursor: 'pointer' }} onClick={handleOpenSubmit}>
          ➕ Submit Your Prompt
        </button>
      </Box> */}

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        ContentProps={{ style: { background: snackbar.severity === 'error' ? '#d32f2f' : '#388e3c', color: '#fff' } }}
      />

      {/* View Modal */}
      <Modal open={viewModal.open} onClose={handleCloseView}>
        <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: 380, bgcolor: '#fff', borderRadius: 2, boxShadow: 24, p: 3, maxHeight: '80vh', overflowY: 'auto' }}>
          {viewModal.prompt && (
            <>
              <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>{viewModal.prompt.title}</Typography>
              <Typography fontSize={14} color="text.secondary" sx={{ mb: 2, whiteSpace: 'pre-line' }}>{viewModal.prompt.content}</Typography>
              {viewModal.prompt.description && (
                <Typography fontSize={13} color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>{viewModal.prompt.description}</Typography>
              )}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 2 }}>
                {viewModal.prompt.tags.split(',').map((tag: string) => (
                  <Chip key={tag.trim()} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag.trim()} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                ))}
                <Chip icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={viewModal.prompt.compatible_llms} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                <span>👍 {viewModal.prompt.upvotes}</span>
                <span>👎 {viewModal.prompt.downvotes}</span>
                <span>⭐ {viewModal.prompt.rating_score.toFixed(1)}</span>
                <span>📅 {new Date(viewModal.prompt.created_at).toLocaleDateString()}</span>
                {viewModal.prompt.is_featured && <span>🏅 Featured</span>}
                <span style={{ marginLeft: 'auto', color: '#2563eb', fontWeight: 500 }}>👤 {viewModal.prompt.user}</span>
              </Box>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end', mt: 2 }}>
                <IconButton color="primary" size="small" onClick={() => viewModal.prompt && handleUse(viewModal.prompt.content)}><RocketLaunchIcon /></IconButton>
                <IconButton size="small" onClick={() => viewModal.prompt && handleSave(viewModal.prompt.title, viewModal.prompt.content)}><SaveIcon /></IconButton>
                <IconButton size="small" onClick={() => viewModal.prompt && handleCopy(viewModal.prompt.content)}><ContentCopyIcon /></IconButton>
              </Box>
            </>
          )}
        </Box>
      </Modal>

      {/* Submit Modal */}
      <PromptSaveModal
        open={submitModalOpen}
        onClose={handleCloseSubmit}
        onSave={handleSubmitPrompt}
        allFolders={[]}
        mode="add"
      />
    </Box>
  );
} 