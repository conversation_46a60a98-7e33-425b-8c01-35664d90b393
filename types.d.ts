declare namespace chrome {
  namespace runtime {
    interface MessageSender {
      id?: string;
      url?: string;
      tab?: chrome.tabs.Tab;
      frameId?: number;
    }

    function getURL(path: string): string;
    function sendMessage(message: any): Promise<any>;
    const onMessage: {
      addListener(callback: (message: any, sender: MessageSender, sendResponse: (response?: any) => void) => void): void;
      removeListener(callback: (message: any, sender: MessageSender, sendResponse: (response?: any) => void) => void): void;
    };
  }

  namespace storage {
    interface StorageArea {
      get(keys: string | string[] | null, callback: (items: { [key: string]: any }) => void): void;
      set(items: { [key: string]: any }, callback?: () => void): void;
    }

    const local: StorageArea;
  }

  namespace offscreen {
    enum Reason {
      DOM_SCRAPING = 'DOM_SCRAPING'
    }

    function createDocument(params: {
      url: string;
      reasons: Reason[];
      justification: string;
    }): Promise<void>;

    function closeDocument(): Promise<void>;
  }

  namespace clients {
    function matchAll(): Promise<Client[]>;
  }
}

interface Client {
  url: string;
}

interface ImportMetaEnv {
  readonly FIREBASE_CLIENT_ID: string;
  // Add other environment variables here
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
} 