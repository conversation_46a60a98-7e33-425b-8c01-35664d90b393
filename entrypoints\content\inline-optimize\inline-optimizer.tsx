import React, { useState, useEffect, useRef, MutableRefObject } from 'react';
import { createRoot } from 'react-dom/client';
import { 
  Button, 
  Box, 
  Paper, 
  Typography, 
  TextField, 
  IconButton, 
  Snackbar,
  Alert,
  Chip,
  Divider,
  Fade,
  Grow,
  Tooltip
} from '@mui/material';
import { 
  AutoAwesome as OptimizeIcon,
  ContentCopy as CopyIcon,
  SwapHoriz as ReplaceIcon,
  Add as InsertIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { handlePerfectPrompt, OptimizedPrompt as PerfectPromptOptimized } from '../../../utils/perfectPrompt';
import { validateApiKeys } from '../../../utils/config';
import { getCurrentChatbot, getMessages, isSubmitButtonLoading, getMessageContent } from '../../../utils/chatbotHandlers';

// Styled components for inline-like appearance
const OptimizeButton = styled(But<PERSON>)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  gap: 6,
  minWidth: 0,
  minHeight: 0,
  height: 28,
  padding: '0 12px',
  fontSize: 13,
  borderRadius: 16,
  background: '#fff',
  color: '#1976d2',
  border: '1px solid #e0e0e0',
  boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
  fontWeight: 500,
  textTransform: 'none',
  letterSpacing: 0,
  transition: 'background 0.15s, box-shadow 0.15s, color 0.15s',
  zIndex: 1000,
  cursor: 'pointer',
  '&:hover': {
    background: '#f5faff',
    boxShadow: '0 2px 8px rgba(25, 118, 210, 0.10)',
    color: '#1565c0',
    borderColor: '#b3d1f7',
  },
  '& .MuiSvgIcon-root': {
    fontSize: 18,
  },
}));

const SideFlyoutPanel = styled(Paper)(({ theme }) => ({
  width: '100%',
  maxWidth: '100%',
  maxHeight: '90vh',
  padding: '18px 18px 14px 18px',
  borderRadius: '14px',
  boxShadow: 'none',
  border: 'none',
  backgroundColor: 'transparent',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'stretch',
}));

const OptimizedSuggestion = styled(Box)(({ theme }) => ({
  padding: '12px',
  margin: '8px 0',
  borderRadius: '8px',
  border: '1px solid #e3f2fd',
  backgroundColor: '#f8fbff',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: '#e3f2fd',
    borderColor: '#1976d2',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  margin: '4px',
  fontSize: '12px',
  padding: '6px 12px',
  borderRadius: '16px',
  textTransform: 'none',
  '&.replace': {
    backgroundColor: '#1976d2',
    color: 'white',
    '&:hover': {
      backgroundColor: '#1565c0',
    }
  },
  '&.copy': {
    backgroundColor: '#f5f5f5',
    color: '#666',
    '&:hover': {
      backgroundColor: '#e0e0e0',
    }
  },
  '&.insert': {
    backgroundColor: '#4caf50',
    color: 'white',
    '&:hover': {
      backgroundColor: '#388e3c',
    }
  }
}));

const FloatingButtonGroup = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'inline-flex',
  borderRadius: '16px',
  overflow: 'hidden',
  border: '1px solid #e0e0e0',
  boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
  backgroundColor: '#fff',
}));

const FloatingIconButton = styled(IconButton)(({ theme }) => ({
  borderRadius: 0,
  padding: '4px',
  height: '24px',
  width: '30px',
  '&:hover': {
    backgroundColor: '#f5faff',
  },
  '&:first-of-type': {
    borderTopLeftRadius: '16px',
    borderBottomLeftRadius: '16px',
  },
  '&:last-of-type': {
    borderTopRightRadius: '16px',
    borderBottomRightRadius: '16px',
  },
}));

const ToastContainer = styled(Box)(({ theme }) => ({
  position: 'fixed',
  bottom: '24px',
  right: '24px',
  zIndex: 10000,
}));

interface OptimizedPrompt {
  id: string;
  text: string;
  style: string;
}

interface InlineOptimizerProps {
  inputField: HTMLElement;
  onClose: () => void;
}

// Add CSS for the fixed flyout container
const flyoutContainerStyle = `
.optimize-prompt-flyout-container {
  position: fixed;
  right: 32px;
  top: 32px;
  width: 360px;
  max-width: 95vw;
  max-height: 90vh;
  z-index: 99999;
  background: rgba(255,255,255,0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.14);
  border: 1px solid #e0e0e0;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: opacity 0.2s, transform 0.2s;
}
.optimize-prompt-flyout-container.hidden {
  opacity: 0;
  pointer-events: none;
  transform: translateY(20px) scale(0.98);
}
`;

// Inject style if not already present
if (!document.getElementById('optimize-prompt-flyout-style')) {
  const style = document.createElement('style');
  style.id = 'optimize-prompt-flyout-style';
  style.textContent = flyoutContainerStyle;
  document.head.appendChild(style);
}

// Helper to create the flyout container
function getOrCreateFlyoutContainer() {
  let container = document.querySelector('.optimize-prompt-flyout-container') as HTMLElement;
  if (!container) {
    container = document.createElement('div');
    container.className = 'optimize-prompt-flyout-container hidden';
    document.body.appendChild(container);
  }
  return container;
}

// Define the flyout panel as a separate component
const OptimizePromptFlyoutPanel: React.FC<{
  onClose: () => void;
  selectedModel: 'openai' | 'gemini';
  apiKeysValid: { openai: boolean; gemini: boolean };
  handleChangeModel: (model: 'openai' | 'gemini') => void;
  originalPrompt: string;
  optimizedPrompts: OptimizedPrompt[];
  isLoading: boolean;
  selectedPrompt: string;
  setSelectedPrompt: (text: string) => void;
  handleReplace: () => void;
  handleCopy: () => void;
  handleInsertBelow: () => void;
  validateApiKeys: () => { openai: boolean; gemini: boolean };
  getTypeLabel: (style: string) => string;
  getTypeColor: (style: string) => string;
}> = ({
  onClose,
  selectedModel,
  apiKeysValid,
  handleChangeModel,
  originalPrompt,
  optimizedPrompts,
  isLoading,
  selectedPrompt,
  setSelectedPrompt,
  handleReplace,
  handleCopy,
  handleInsertBelow,
  validateApiKeys,
  getTypeLabel,
  getTypeColor,
}) => {
  return (
    <SideFlyoutPanel elevation={0}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 600 }}>
          ✨ Optimize Prompt
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton size="small" onClick={onClose}>
            <CloseIcon sx={{ fontSize: 18 }} />
          </IconButton>
        </Box>
      </Box>
      {/* <Box sx={{ display: 'flex',  alignItems: 'center', justifyContent: 'flex-end', gap: 0.5, mb: 2 }}>
        <Typography variant="caption" sx={{ color: '#666', fontSize: '11px' }}>
          Model:
        </Typography>
        <Box sx={{ display: 'flex', border: '1px solid #e0e0e0', borderRadius: '12px', overflow: 'hidden' }}>
          <Button
            size="small"
            onClick={() => handleChangeModel('openai')}
            disabled={!apiKeysValid.openai}
            sx={{
              fontSize: '10px',
              padding: '2px 8px',
              minWidth: 'auto',
              backgroundColor: selectedModel === 'openai' ? '#1976d2' : 'transparent',
              color: selectedModel === 'openai' ? 'white' : apiKeysValid.openai ? '#666' : '#ccc',
              borderRadius: 0,
              textTransform: 'none',
              opacity: apiKeysValid.openai ? 1 : 0.5,
              '&:hover': {
                backgroundColor: selectedModel === 'openai' ? '#1565c0' : '#f5f5f5'
              },
              '&:disabled': {
                backgroundColor: 'transparent',
                color: '#ccc'
              }
            }}
            title={apiKeysValid.openai ? 'Use OpenAI GPT-4' : 'OpenAI API key not configured'}
          >
            OpenAI
          </Button>
          <Button
            size="small"
            onClick={() => handleChangeModel('gemini')}
            disabled={!apiKeysValid.gemini}
            sx={{
              fontSize: '10px',
              padding: '2px 8px',
              minWidth: 'auto',
              backgroundColor: selectedModel === 'gemini' ? '#1976d2' : 'transparent',
              color: selectedModel === 'gemini' ? 'white' : apiKeysValid.gemini ? '#666' : '#ccc',
              borderRadius: 0,
              textTransform: 'none',
              opacity: apiKeysValid.gemini ? 1 : 0.5,
              '&:hover': {
                backgroundColor: selectedModel === 'gemini' ? '#1565c0' : '#f5f5f5'
              },
              '&:disabled': {
                backgroundColor: 'transparent',
                color: '#ccc'
              }
            }}
            title={apiKeysValid.gemini ? 'Use Google Gemini' : 'Gemini API key not configured'}
          >
            Gemini
          </Button>
        </Box>
      </Box> */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
          Original Prompt
        </Typography>
        <TextField
          multiline
          rows={2}
          value={originalPrompt}
          variant="outlined"
          size="small"
          fullWidth
          sx={{
            '& .MuiOutlinedInput-root': {
              fontSize: '14px',
              backgroundColor: '#f5f5f5'
            }
          }}
        />
      </Box>
      {/* API Key Warning */}
      {!apiKeysValid.openai && !apiKeysValid.gemini && (
        <Box sx={{ mb: 2, p: 2, backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', borderRadius: '8px' }}>
          <Typography variant="body2" sx={{ color: '#856404', fontSize: '12px', mb: 1 }}>
            ⚠️ No API keys configured
          </Typography>
          <Typography variant="caption" sx={{ color: '#856404', fontSize: '11px', display: 'block', mb: 1 }}>
            Configure your OpenAI or Gemini API key in the extension settings to use real prompt optimization.
          </Typography>
          <Button
            size="small"
            onClick={validateApiKeys}
            sx={{
              fontSize: '10px',
              padding: '4px 8px',
              backgroundColor: '#856404',
              color: 'white',
              textTransform: 'none',
              '&:hover': {
                backgroundColor: '#6c5ce7'
              }
            }}
          >
            Retry
          </Button>
        </Box>
      )}
      <Divider sx={{ my: 2 }} />
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
          Optimized Suggestions
        </Typography>
        {isLoading ? (
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Optimizing with {selectedModel === 'openai' ? 'OpenAI GPT-4' : 'Google Gemini'}...
            </Typography>
          </Box>
        ) : (
          <Box sx={{ 
            maxHeight: '350px', 
            overflowY: 'auto',
            overflowX: 'hidden',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '3px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#c1c1c1',
              borderRadius: '3px',
              '&:hover': {
                background: '#a8a8a8',
              },
            },
          }}>
            {optimizedPrompts.map((prompt) => (
              <OptimizedSuggestion
                key={prompt.id}
                onClick={() => setSelectedPrompt(prompt.text)}
                sx={{
                  borderColor: selectedPrompt === prompt.text ? getTypeColor(prompt.style) : '#e3f2fd',
                  backgroundColor: selectedPrompt === prompt.text ? '#e3f2fd' : '#f8fbff',
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Chip
                    label={getTypeLabel(prompt.style)}
                    size="small"
                    sx={{
                      backgroundColor: getTypeColor(prompt.style),
                      color: 'white',
                      fontSize: '10px',
                      height: '20px'
                    }}
                  />
                </Box>
                <Typography variant="body2" sx={{ fontSize: '13px', lineHeight: 1.4 }}>
                  {prompt.text}
                </Typography>
              </OptimizedSuggestion>
            ))}
          </Box>
        )}
      </Box>
      {!isLoading && optimizedPrompts.length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <ActionButton
            className="replace"
            onClick={handleReplace}
            startIcon={<ReplaceIcon sx={{ fontSize: 14 }} />}
          >
            Replace
          </ActionButton>
          <ActionButton
            className="copy"
            onClick={handleCopy}
            startIcon={<CopyIcon sx={{ fontSize: 14 }} />}
          >
            Copy
          </ActionButton>
          <ActionButton
            className="insert"
            onClick={handleInsertBelow}
            startIcon={<InsertIcon sx={{ fontSize: 14 }} />}
          >
            Insert Below
          </ActionButton>
        </Box>
      )}
    </SideFlyoutPanel>
  );
};

const InlineOptimizer: React.FC<InlineOptimizerProps> = ({ inputField, onClose }) => {
  // Move getTypeLabel and getTypeColor above useEffect
  const getTypeLabel = (style: string) => {
    const styleMap: { [key: string]: string } = {
      'detailed': 'Detailed',
      'clear': 'Clear',
      'concise': 'Concise',
      'instructional': 'Instructional',
      'creative': 'Creative',
      'formal': 'Formal',
      'professional': 'Professional',
      'friendly': 'Friendly',
      'technical': 'Technical',
      'simple': 'Simple'
    };
    return styleMap[style.toLowerCase()] || style;
  };

  const getTypeColor = (style: string) => {
    const colorMap: { [key: string]: string } = {
      'detailed': '#1976d2',
      'clear': '#4caf50',
      'concise': '#ff9800',
      'instructional': '#9c27b0',
      'creative': '#e91e63',
      'formal': '#607d8b',
      'professional': '#3f51b5',
      'friendly': '#4caf50',
      'technical': '#ff5722',
      'simple': '#795548'
    };
    return colorMap[style.toLowerCase()] || '#666';
  };

  const [showOptimizeButton, setShowOptimizeButton] = useState(false);
  const [showFlyout, setShowFlyout] = useState(false);
  const [originalPrompt, setOriginalPrompt] = useState('');
  const [optimizedPrompts, setOptimizedPrompts] = useState<OptimizedPrompt[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'info' } | null>(null);
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<'openai' | 'gemini'>('gemini');
  const [apiKeysValid, setApiKeysValid] = useState<{ openai: boolean; gemini: boolean }>({ openai: false, gemini: false });
  
  const buttonRef = useRef<HTMLDivElement>(null);
  const flyoutRootRef: MutableRefObject<ReturnType<typeof createRoot> | null> = useRef(null);
  const flyoutContainerRef = useRef<HTMLElement | null>(null);

  // Check API keys on component mount
  useEffect(() => {
    const keys = validateApiKeys();
    setApiKeysValid(keys);
    
    // Set default model based on available API keys
    if (keys.gemini) {
      setSelectedModel('gemini');
    } else if (keys.openai) {
      setSelectedModel('openai');
    }
  }, []);

  // Real optimization function using perfectPrompt.ts logic
  const optimizePrompt = async (prompt: string, model: 'openai' | 'gemini' = selectedModel): Promise<OptimizedPrompt[]> => {
    try {
      // Check if selected model has valid API key
      if (!apiKeysValid[model]) {
        throw new Error(`${model} API key not configured`);
      }

      // Use the real perfectPrompt logic with selected model
      const perfectPrompts = await handlePerfectPrompt(prompt, model);
      
      // Convert PerfectPrompt format to our local format
      return perfectPrompts.map((prompt: PerfectPromptOptimized, index: number) => ({
        id: prompt.id,
        text: prompt.text,
        style: prompt.style
      }));
    } catch (error) {
      console.error('Failed to optimize prompt:', error);
      setToast({ message: `Failed to optimize: ${error instanceof Error ? error.message : 'Unknown error'}`, type: 'info' });
      // Fallback to mock data if API fails
      return [
        {
          id: '1',
          text: `You are an expert AI assistant. Please help me with the following request: ${prompt}. Please provide a clear, detailed response with specific examples and actionable steps.`,
          style: 'Detailed'
        },
        {
          id: '2',
          text: `As a knowledgeable assistant, I need your help with: ${prompt}. Please give me a concise but comprehensive answer.`,
          style: 'Clear'
        },
        {
          id: '3',
          text: `Help me with: ${prompt}. Provide a brief, focused response.`,
          style: 'Concise'
        }
      ];
    }
  };

  // Handle input changes to show/hide optimize button
  useEffect(() => {
    const handleInput = (e: Event) => {
      const target = e.target as HTMLElement;
      let text = '';
      
      // Handle different types of input elements
      if (target instanceof HTMLInputElement || target instanceof HTMLTextAreaElement) {
        text = target.value;
      } else if (target.hasAttribute('contenteditable')) {
        // Handle contenteditable divs
        text = target.textContent || target.innerText || '';
      } else {
        // Fallback for other elements
        text = (target as any).value || target.textContent || target.innerText || '';
      }
      
      const hasText = text.trim().length > 0;
      setShowOptimizeButton(hasText);
      
      if (hasText && !showFlyout) {
        setOriginalPrompt(text);
      }
    };

    inputField.addEventListener('input', handleInput);
    inputField.addEventListener('keyup', handleInput);
    
    return () => {
      inputField.removeEventListener('input', handleInput);
      inputField.removeEventListener('keyup', handleInput);
    };
  }, [inputField, showFlyout]);

  // Handle optimize button click
  const handleOptimizeClick = async () => {
    // Get the latest value from the input field
    let text = '';
    if (inputField instanceof HTMLInputElement || inputField instanceof HTMLTextAreaElement) {
      text = inputField.value;
    } else if (inputField.hasAttribute('contenteditable')) {
      text = inputField.textContent || '';
    }
    setOriginalPrompt(text);

    setIsLoading(true);
    setShowFlyout(true);
    
    try {
      const optimized = await optimizePrompt(text, selectedModel);
      setOptimizedPrompts(optimized);
      setSelectedPrompt(optimized[0].text);
    } catch (error) {
      console.error('Failed to optimize prompt:', error);
      setToast({ message: 'Failed to optimize prompt', type: 'info' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangeModel = async (model: 'openai' | 'gemini') => {
    setSelectedModel(model);
    // Get the latest value from the input field
    let text = '';
    if (inputField instanceof HTMLInputElement || inputField instanceof HTMLTextAreaElement) {
      text = inputField.value;
    } else if (inputField.hasAttribute('contenteditable')) {
      text = inputField.textContent || '';
    }
    setOriginalPrompt(text);

    setIsLoading(true);
    setShowFlyout(true);
    
    try {
      const optimized = await optimizePrompt(text, model);
      setOptimizedPrompts(optimized);
      setSelectedPrompt(optimized[0].text);
    } catch (error) {
      console.error('Failed to optimize prompt:', error);
      setToast({ message: 'Failed to optimize prompt', type: 'info' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle action buttons
  const handleReplace = () => {
    if (inputField instanceof HTMLInputElement || inputField instanceof HTMLTextAreaElement) {
      inputField.value = selectedPrompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (inputField.hasAttribute('contenteditable')) {
      // Handle contenteditable divs
      inputField.textContent = selectedPrompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    }
    setToast({ message: 'Prompt replaced successfully!', type: 'success' });
    setShowFlyout(false);
    
    // Trigger a custom event to notify the main content script that a prompt was replaced
    // This will help with bookmark monitoring
    const replaceEvent = new CustomEvent('promptReplaced', {
      detail: { inputField, prompt: selectedPrompt },
      bubbles: true
    });
    document.dispatchEvent(replaceEvent);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(selectedPrompt);
    setToast({ message: 'Prompt copied to clipboard!', type: 'success' });
  };

  const handleInsertBelow = () => {
    if (inputField instanceof HTMLInputElement || inputField instanceof HTMLTextAreaElement) {
      const currentValue = inputField.value;
      inputField.value = currentValue + '\n\n' + selectedPrompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (inputField.hasAttribute('contenteditable')) {
      // Handle contenteditable divs
      const currentText = inputField.textContent || '';
      inputField.textContent = currentText + '\n\n' + selectedPrompt;
      inputField.dispatchEvent(new Event('input', { bubbles: true }));
    }
    setToast({ message: 'Prompt inserted below!', type: 'success' });
    setShowFlyout(false);
  };

  const handleOpenPopup = async () => {
    try {
      await browser.runtime.sendMessage({ action: 'openPopup' });
    } catch (error) {
      console.log('Failed to open popup:', error);
    }
  };

  // Create the flyout container and root only once
  useEffect(() => {
    const container = getOrCreateFlyoutContainer();
    flyoutContainerRef.current = container;
    if (!flyoutRootRef.current) {
      flyoutRootRef.current = createRoot(container);
    }
    return () => {
      if (flyoutRootRef.current) {
        flyoutRootRef.current.unmount();
        flyoutRootRef.current = null;
      }
      if (flyoutContainerRef.current) {
        flyoutContainerRef.current.classList.add('hidden');
        flyoutContainerRef.current.innerHTML = '';
      }
    };
  }, []);

  // Always render the panel into the root with latest props
  useEffect(() => {
    if (!flyoutRootRef.current || !flyoutContainerRef.current) return;
    if (showFlyout) {
      flyoutContainerRef.current.classList.remove('hidden');
      flyoutRootRef.current.render(
        <OptimizePromptFlyoutPanel
          onClose={() => setShowFlyout(false)}
          selectedModel={selectedModel}
          apiKeysValid={apiKeysValid}
          handleChangeModel={handleChangeModel}
          originalPrompt={originalPrompt}
          optimizedPrompts={optimizedPrompts}
          isLoading={isLoading}
          selectedPrompt={selectedPrompt}
          setSelectedPrompt={setSelectedPrompt}
          handleReplace={handleReplace}
          handleCopy={handleCopy}
          handleInsertBelow={handleInsertBelow}
          validateApiKeys={() => {
            const keys = validateApiKeys();
            setApiKeysValid(keys);
            return keys;
          }}
          getTypeLabel={getTypeLabel}
          getTypeColor={getTypeColor}
        />
      );
    } else {
      flyoutContainerRef.current.classList.add('hidden');
    }
  }, [showFlyout, selectedModel, apiKeysValid, handleChangeModel, originalPrompt, optimizedPrompts, isLoading, selectedPrompt, setSelectedPrompt, handleReplace, handleCopy, handleInsertBelow, getTypeLabel, getTypeColor]);

  // Only render the optimize button inline
  return (
    <>
      {showOptimizeButton && !showFlyout && (
        <Box ref={buttonRef} sx={{ position: 'relative', display: 'inline-block' }}>
          <FloatingButtonGroup>
            <Tooltip title="Optimize Prompt">
              <FloatingIconButton
                onClick={handleOptimizeClick}
                disabled={isLoading}
                aria-label="Optimize prompt"
              >
                <OptimizeIcon sx={{ fontSize: 16, color: '#FFD700' }} />
              </FloatingIconButton>
            </Tooltip>
            <Divider orientation="vertical" flexItem />
            <Tooltip title="Click to open assistant">
              <FloatingIconButton
                onClick={handleOpenPopup}
                aria-label="Click to open assistant"
              >
                <img src={chrome.runtime.getURL('icon/32.png')} alt="logo" style={{ width: 16, height: 16 }} />
              </FloatingIconButton>
            </Tooltip>
          </FloatingButtonGroup>
        </Box>
      )}
      {/* Toast Notifications */}
      <ToastContainer>
        <Snackbar
          open={!!toast}
          autoHideDuration={3000}
          onClose={() => setToast(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setToast(null)}
            severity={toast?.type}
            sx={{ width: '100%' }}
          >
            {toast?.message}
          </Alert>
        </Snackbar>
      </ToastContainer>
    </>
  );
};

export default InlineOptimizer;