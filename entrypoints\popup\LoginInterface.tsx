import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ton,
  Typography,
  Alert,
  CircularProgress,
  <PERSON>,
  CardContent,
  Divider,
} from "@mui/material";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import {
  savePromptCircleAuthState,
  clearPromptCircleAuthState,
  getAuthState,
} from "@/utils/auth";
import type { AuthState, PromptCircleUserInfo } from "@/types/auth";

// Message types for PromptCircle API communication
interface PromptCircleAuthResult {
  type: "PROMPTCIRCLE_AUTH_RESULT";
  success: boolean;
  isAuthenticated?: boolean;
  error?: string;
}

interface PromptCircleSessionInfoResult {
  type: "PROMPTCIRCLE_SESSION_INFO_RESULT";
  success: boolean;
  sessionInfo?: any;
  error?: string;
}

interface LoginInterfaceProps {
  onLoginSuccess: (authState: AuthState) => void;
  onOpenOptimizer: () => void;
}

export default function LoginInterface({
  onLoginSuccess,
  onOpenOptimizer,
}: LoginInterfaceProps) {
  const [error, setError] = useState<string | null>(null);
  const [promptCircleAuth, setPromptCircleAuth] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is authenticated with PromptCircle
    const checkPromptCircleAuth = async () => {
      try {
        const response = (await chrome.runtime.sendMessage({
          type: "PROMPTCIRCLE_CHECK_AUTH",
        })) as unknown as PromptCircleAuthResult;

        if (response && response.success) {
          setPromptCircleAuth(response.isAuthenticated || false);
          if (response.isAuthenticated) {
            // Get session info and update auth state
            await handlePromptCircleAuthSuccess();
          }
        }
      } catch (error) {
        console.error("Error checking PromptCircle auth state:", error);
      }
    };

    // Set up interval to check auth state periodically (for when user logs in via opened tab)
    const authCheckInterval = setInterval(checkPromptCircleAuth, 2000);

    // Initial check
    checkPromptCircleAuth();

    // Cleanup interval on unmount
    return () => {
      clearInterval(authCheckInterval);
    };
  }, [onLoginSuccess]);

  // Handle successful PromptCircle authentication
  const handlePromptCircleAuthSuccess = async () => {
    try {
      // Get session info from PromptCircle
      const sessionResponse = (await chrome.runtime.sendMessage({
        type: "PROMPTCIRCLE_GET_SESSION_INFO",
      })) as unknown as PromptCircleSessionInfoResult;

      if (
        sessionResponse &&
        sessionResponse.success &&
        sessionResponse.sessionInfo
      ) {
        // Extract user info from session
        const sessionInfo = sessionResponse.sessionInfo;
        const promptCircleUserInfo: PromptCircleUserInfo = {
          email: sessionInfo.user?.email || sessionInfo.email || "",
          username: sessionInfo.user?.username || sessionInfo.username,
          first_name: sessionInfo.user?.first_name || sessionInfo.first_name,
          last_name: sessionInfo.user?.last_name || sessionInfo.last_name,
          ...sessionInfo.user, // Include any additional fields
        };

        // Save PromptCircle auth state
        await savePromptCircleAuthState(promptCircleUserInfo);

        // Get updated auth state and call onLoginSuccess
        const updatedAuthState = await getAuthState();
        onLoginSuccess(updatedAuthState);
      }
    } catch (error) {
      console.error("Error handling PromptCircle auth success:", error);
    }
  };

  const handlePromptCircleLogin = () => {
    chrome.runtime.sendMessage({
      type: "PROMPTCIRCLE_OPEN_LOGIN",
    });
  };

  const handlePromptCircleLogout = async () => {
    try {
      const response = (await chrome.runtime.sendMessage({
        type: "PROMPTCIRCLE_LOGOUT",
      })) as unknown as PromptCircleAuthResult;

      if (response && response.success) {
        setPromptCircleAuth(false);
        // Clear PromptCircle auth state from storage
        await clearPromptCircleAuthState();
      }
    } catch (error) {
      console.error("Error logging out from PromptCircle:", error);
    }
  };

  const handleOpenOptimizer = () => {
    onOpenOptimizer();
  };

  return (
    <Box
      sx={{
        width: "350px",
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        gap: 2,
        background: "#fafafa",
        fontFamily: "Arial, sans-serif",
      }}
    >
      {/* Header */}
      <Box sx={{ textAlign: "center", mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 1,
            mb: 1,
          }}
        >
          <img
            src={chrome.runtime.getURL("/icon/48.png")}
            alt="PromptPilot Logo"
            style={{ height: 32, width: 32 }}
          />
          <Typography variant="h5" sx={{ fontWeight: 700, color: "#e0904e" }}>
            PromptPilot
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          AI Prompt Optimizer
        </Typography>
      </Box>

      {/* Authentication Status */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        {/* PromptCircle Authentication Status */}
        <Card
          sx={{
            background: promptCircleAuth ? "#d4edda" : "#f8d7da",
            border: promptCircleAuth
              ? "1px solid #c3e6cb"
              : "1px solid #f5c6cb",
          }}
        >
          <CardContent sx={{ py: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: promptCircleAuth ? "#155724" : "#721c24",
                  fontWeight: 500,
                }}
              >
                {promptCircleAuth
                  ? "✅ PromptCircle Authenticated"
                  : "❌ PromptCircle Not Authenticated"}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Action Buttons */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        {/* PromptCircle Authentication */}
        {!promptCircleAuth ? (
          <Button
            variant="contained"
            onClick={handlePromptCircleLogin}
            sx={{
              background: "#667eea",
              "&:hover": { background: "#5a6fd8" },
              py: 1.5,
            }}
          >
            Login to PromptCircle
          </Button>
        ) : (
          <Button
            variant="outlined"
            onClick={handlePromptCircleLogout}
            sx={{ py: 1.5 }}
          >
            Logout from PromptCircle
          </Button>
        )}

        {/* Open Optimizer Button - Only show if authenticated */}
        {promptCircleAuth && (
          <Button
            variant="contained"
            startIcon={<SmartToyIcon />}
            onClick={handleOpenOptimizer}
            sx={{
              background: "#28a745",
              "&:hover": { background: "#218838" },
              py: 1.5,
              mt: 1,
            }}
          >
            Open Optimizer
          </Button>
        )}
      </Box>

      {/* Features Preview */}
      <Divider sx={{ my: 2 }} />
      <Box>
        <Typography variant="h6" sx={{ mb: 1, fontSize: "1rem" }}>
          Features
        </Typography>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
          <Typography variant="body2" color="text.secondary">
            • AI-powered prompt optimization
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Works with ChatGPT, Claude, Grok, Gemini
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Save and organize your best prompts
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Community prompt sharing
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
