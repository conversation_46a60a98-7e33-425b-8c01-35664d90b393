export interface UserInfo {
  email: string;
  name: string;
  picture: string;
  sub: string;
}

export interface PromptCircleUserInfo {
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  [key: string]: any; // Allow for additional fields from PromptCircle session
}

export interface AuthState {
  isAuthenticated: boolean;
  userInfo: UserInfo | null;
  accessToken: string | null;
  promptCircleAuth?: {
    isAuthenticated: boolean;
    userInfo: PromptCircleUserInfo | null;
  };
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}
