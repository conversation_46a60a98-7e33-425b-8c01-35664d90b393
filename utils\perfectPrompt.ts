import { API_KEYS } from './config';
import { promptCircle<PERSON>pi, type OptimizationResponse, type ApiError } from './promptCircleApi';

// Configuration for the prompt optimization
export const PROMPT_CONFIG = {
  systemPrompt: `You are an expert prompt engineer for Large Language Models (LLMs) such as ChatGPT, Claude, and Gemini.

Your job is to improve user-written prompts so they become clearer, more specific, and more effective when used with an LLM.

You must return 2–3 optimized versions of the original prompt. Each version should reflect a clearly different communication style, tone, or structure — such as concise, detailed, instructional, creative, or formal.

Tips for optimizing:
- Always start the prompt with a relevant role or expert persona (e.g., "As a senior data analyst…").
- Place the instruction first. Use clear separators like "###" to separate the instruction and context.
- Add details such as audience, output format (list, table, paragraph), tone, length, or purpose.
- You may enhance the prompt with minimal inferred context — but never change the user's intent.

Guidelines:
- Preserve the user's original task and intent.
- **Always detect and strictly respond in the language used in the user's original prompt. Do not translate, switch, or mix languages under any circumstances.**
- Do not include any explanations, commentary, or introductions.
- Format strictly as:
  1. [Style Label]: [Improved Prompt]
  2. [Style Label]: [Improved Prompt]

IMPORTANT: Never explain what you did - just return the improved prompt in the same language as the original.`
};

// Interface for optimized prompt suggestions
export interface OptimizedPrompt {
  id: string;
  text: string;
  style: string;
}

// Interface for PromptCircle API suggestions
export interface PromptCircleSuggestion {
  label: string;
  content: string;
}

// Enhanced interface that can handle both local and PromptCircle responses
export interface OptimizationResult {
  source: 'promptcircle' | 'local';
  suggestions: OptimizedPrompt[];
  error?: string;
}

// Convert PromptCircle suggestions to OptimizedPrompt format
export function convertPromptCircleSuggestions(suggestions: PromptCircleSuggestion[]): OptimizedPrompt[] {
  return suggestions.map((suggestion, index) => ({
    id: `promptcircle-${index + 1}`,
    text: suggestion.content,
    style: suggestion.label
  }));
}

// Parse the AI response to extract multiple optimized prompts
export function parseOptimizedPrompts(response: string): OptimizedPrompt[] {
  const prompts: OptimizedPrompt[] = [];
  const lines = response.split('\n').filter(line => line.trim());
  
  let currentPrompt = '';
  let currentStyle = '';
  
  for (const line of lines) {
    // Check if line starts with a number (1., 2., 3.)
    const numberedMatch = line.match(/^(\d+)\.\s*\[([^\]]+)\]:\s*(.+)$/);
    if (numberedMatch) {
      // Save previous prompt if exists
      if (currentPrompt && currentStyle) {
        prompts.push({
          id: `prompt-${prompts.length + 1}`,
          text: currentPrompt.trim(),
          style: currentStyle.trim()
        });
      }
      
      currentStyle = numberedMatch[2];
      currentPrompt = numberedMatch[3];
      
    } else {
      // Continue building current prompt
      if (currentPrompt) {
        currentPrompt += '\n' + line;
      }
    }
  }
  
  // Add the last prompt
  if (currentPrompt && currentStyle) {
    prompts.push({
      id: `prompt-${prompts.length + 1}`,
      text: currentPrompt.trim(),
      style: currentStyle.trim()
    });
  }
  
  // If parsing failed, return the original response as a single prompt
  if (prompts.length === 0) {
    return [{
      id: 'prompt-1',
      text: response.trim(),
      style: 'Optimized'
    }];
  }
  
  return prompts;
}

// Handle OpenAI API call for prompt optimization
export async function handleOpenAIPrompt(prompt: string): Promise<OptimizedPrompt[]> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_KEYS.OPENAI_API_KEY}`
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: PROMPT_CONFIG.systemPrompt
        },
        {
          role: 'user',
          content: `<user_prompt>\nORIGINAL PROMPT: ${prompt}\n</user_prompt>`
        }
      ],
      temperature: 0.7
    })
  });

  if (!response.ok) {
    throw new Error('Failed to get response from OpenAI');
  }

  const data = await response.json();
  const responseText = data.choices[0].message.content;
  return parseOptimizedPrompts(responseText);
}

// Handle Gemini API call for prompt optimization
export async function handleGeminiPrompt(prompt: string): Promise<OptimizedPrompt[]> {
  if (!API_KEYS.GEMINI_API_KEY) {
    throw new Error('Gemini API key not found');
  }

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEYS.GEMINI_API_KEY}`, {
    method: 'POST',
    headers: new Headers({
      'Content-Type': 'application/json',
      'x-goog-api-key': API_KEYS.GEMINI_API_KEY
    }),
    body: JSON.stringify({
      contents: [{
        role: 'user',
        parts: [{
          text: `${PROMPT_CONFIG.systemPrompt}\n\nBelow is the user's prompt:\n<user_prompt>\nORIGINAL PROMPT: ${prompt}\n</user_prompt>`
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to get response from Gemini: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
    throw new Error('Invalid response format from Gemini');
  }
  
  const responseText = data.candidates[0].content.parts[0].text;
  
  const parsedPrompts = parseOptimizedPrompts(responseText);
  
  return parsedPrompts;
}

// Handle PromptCircle API call for prompt optimization
export async function handlePromptCircleOptimization(prompt: string): Promise<OptimizationResult> {
  try {
    const result = await promptCircleApi.optimizePrompt(prompt);
    
    if ('error' in result) {
      return {
        source: 'promptcircle',
        suggestions: [],
        error: result.error
      };
    }
    
    const suggestions = convertPromptCircleSuggestions(result.suggestions);
    return {
      source: 'promptcircle',
      suggestions
    };
  } catch (error) {
    console.error('Error with PromptCircle optimization:', error);
    return {
      source: 'promptcircle',
      suggestions: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Main function to handle prompt optimization with fallback strategy
export async function handlePerfectPrompt(prompt: string, model: 'openai' | 'gemini' = 'openai'): Promise<OptimizedPrompt[]> {
  try {
    switch (model) {
      case 'openai':
        return await handleOpenAIPrompt(prompt);
      case 'gemini':
        return await handleGeminiPrompt(prompt);
      default:
        throw new Error('Unsupported model');
    }
  } catch (error) {
    console.error('Error handling perfect prompt:', error);
    throw error;
  }
}

// Enhanced function that tries PromptCircle first, then falls back to local optimization
export async function handleOptimizationWithFallback(prompt: string, preferredModel: 'openai' | 'gemini' = 'openai'): Promise<OptimizationResult> {
  try {
    // First, try PromptCircle API
    const promptCircleResult = await handlePromptCircleOptimization(prompt);
    
    if (promptCircleResult.suggestions.length > 0) {
      return promptCircleResult;
    }
    
    // If PromptCircle fails or returns no suggestions, fall back to local optimization
    const localSuggestions = await handlePerfectPrompt(prompt, preferredModel);
    return {
      source: 'local',
      suggestions: localSuggestions
    };
  } catch (error) {
    console.error('Error in optimization with fallback:', error);
    return {
      source: 'local',
      suggestions: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Create and show a modal with optimized prompts
// export function showOptimizedPromptsModal(optimizedPrompts: OptimizedPrompt[], inputField: HTMLElement) {
//   // Remove existing modal if any
//   const existingModal = document.getElementById('perfect-prompt-modal');
//   if (existingModal) {
//     existingModal.remove();
//   }

//   // Create modal container
//   const modal = document.createElement('div');
//   modal.id = 'perfect-prompt-modal';
//   modal.style.cssText = `
//     position: fixed;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 100%;
//     background: rgba(0, 0, 0, 0.5);
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     z-index: 10000;
//     font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
//   `;

//   // Create modal content
//   const modalContent = document.createElement('div');
//   modalContent.style.cssText = `
//     background: white;
//     border-radius: 12px;
//     padding: 24px;
//     max-width: 600px;
//     width: 90%;
//     max-height: 80vh;
//     overflow-y: auto;
//     box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
//   `;

//   // Create header
//   const header = document.createElement('div');
//   header.style.cssText = `
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     margin-bottom: 20px;
//     padding-bottom: 16px;
//     border-bottom: 1px solid #e5e7eb;
//   `;

//   const title = document.createElement('h2');
//   title.textContent = '✨ Optimized Prompts';
//   title.style.cssText = `
//     margin: 0;
//     color: #1f2937;
//     font-size: 20px;
//     font-weight: 600;
//   `;

//   const closeButton = document.createElement('button');
//   closeButton.innerHTML = '&times;';
//   closeButton.style.cssText = `
//     background: none;
//     border: none;
//     font-size: 24px;
//     cursor: pointer;
//     color: #6b7280;
//     padding: 0;
//     width: 32px;
//     height: 32px;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 6px;
//   `;
//   closeButton.onclick = () => modal.remove();

//   header.appendChild(title);
//   header.appendChild(closeButton);

//   // Create prompts container
//   const promptsContainer = document.createElement('div');
//   promptsContainer.style.cssText = `
//     display: flex;
//     flex-direction: column;
//     gap: 16px;
//   `;

//   // Add each optimized prompt
//   optimizedPrompts.forEach((prompt, index) => {
//     const promptCard = document.createElement('div');
//     promptCard.style.cssText = `
//       border: 2px solid #e5e7eb;
//       border-radius: 8px;
//       padding: 16px;
//       cursor: pointer;
//       transition: all 0.2s ease;
//       background: #f9fafb;
//     `;

//     promptCard.onmouseenter = () => {
//       promptCard.style.borderColor = '#3b82f6';
//       promptCard.style.backgroundColor = '#eff6ff';
//     };

//     promptCard.onmouseleave = () => {
//       promptCard.style.borderColor = '#e5e7eb';
//       promptCard.style.backgroundColor = '#f9fafb';
//     };

//     // Style label
//     const styleLabel = document.createElement('div');
//     styleLabel.textContent = prompt.style;
//     styleLabel.style.cssText = `
//       background: #3b82f6;
//       color: white;
//       padding: 4px 8px;
//       border-radius: 12px;
//       font-size: 12px;
//       font-weight: 500;
//       display: inline-block;
//       margin-bottom: 8px;
//     `;

//     // Prompt text
//     const promptText = document.createElement('div');
//     promptText.textContent = prompt.text;
//     promptText.style.cssText = `
//       color: #374151;
//       line-height: 1.5;
//       font-size: 14px;
//       white-space: pre-wrap;
//     `;

//     // Action buttons
//     const actionButtons = document.createElement('div');
//     actionButtons.style.cssText = `
//       display: flex;
//       gap: 8px;
//       margin-top: 12px;
//     `;

//     const replaceButton = document.createElement('button');
//     replaceButton.textContent = 'Replace';
//     replaceButton.style.cssText = `
//       background: #3b82f6;
//       color: white;
//       border: none;
//       padding: 6px 12px;
//       border-radius: 6px;
//       font-size: 12px;
//       cursor: pointer;
//       font-weight: 500;
//     `;
//     replaceButton.onclick = (e) => {
//       e.stopPropagation();
//       applyPromptToInput(inputField, prompt.text);
//       modal.remove();
//     };

//     const copyButton = document.createElement('button');
//     copyButton.textContent = 'Copy';
//     copyButton.style.cssText = `
//       background: #f3f4f6;
//       color: #374151;
//       border: 1px solid #d1d5db;
//       padding: 6px 12px;
//       border-radius: 6px;
//       font-size: 12px;
//       cursor: pointer;
//       font-weight: 500;
//     `;
//     copyButton.onclick = (e) => {
//       e.stopPropagation();
//       navigator.clipboard.writeText(prompt.text);
//       showToast('Prompt copied to clipboard!');
//     };

//     actionButtons.appendChild(replaceButton);
//     actionButtons.appendChild(copyButton);

//     promptCard.appendChild(styleLabel);
//     promptCard.appendChild(promptText);
//     promptCard.appendChild(actionButtons);

//     // Make entire card clickable to replace
//     promptCard.onclick = () => {
//       applyPromptToInput(inputField, prompt.text);
//       modal.remove();
//     };

//     promptsContainer.appendChild(promptCard);
//   });

//   modalContent.appendChild(header);
//   modalContent.appendChild(promptsContainer);
//   modal.appendChild(modalContent);
//   document.body.appendChild(modal);

//   // Close modal when clicking outside
//   modal.onclick = (e) => {
//     if (e.target === modal) {
//       modal.remove();
//     }
//   };
// }

// Helper function to apply prompt to input field
// function applyPromptToInput(inputField: HTMLElement, promptText: string) {
//   if (inputField.isContentEditable) {
//     inputField.innerText = promptText;
//   } else {
//     (inputField as HTMLTextAreaElement | HTMLInputElement).value = promptText;
//   }
  
//   // Trigger input event to notify any listeners
//   inputField.dispatchEvent(new Event('input', { bubbles: true }));
//   inputField.focus();
  
//   showToast('Prompt applied successfully!');
// }

// Helper function to show toast notification
// function showToast(message: string) {
//   const toast = document.createElement('div');
//   toast.textContent = message;
//   toast.style.cssText = `
//     position: fixed;
//     bottom: 24px;
//     right: 24px;
//     background: #10b981;
//     color: white;
//     padding: 12px 16px;
//     border-radius: 8px;
//     font-size: 14px;
//     font-weight: 500;
//     z-index: 10001;
//     box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
//   `;
  
//   document.body.appendChild(toast);
  
//   setTimeout(() => {
//     toast.remove();
//   }, 3000);
// }

// Button click handler for Perfect button
export function assignPerfectButtonListener(button: HTMLButtonElement, inputField: HTMLElement) {
  button.addEventListener('click', async (event) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      button.classList.add('loading');

      const promptText = inputField.isContentEditable
        ? inputField.innerText
        : (inputField as HTMLTextAreaElement | HTMLInputElement).value;

      if (!promptText || promptText.trim().length === 0) {
        alert('Cannot optimize an empty prompt.');
        button.classList.remove('loading');
        return;
      }

      const optimizedPrompts = await handlePerfectPrompt(promptText, 'gemini');
      // if (inputField.isContentEditable) {
      //   inputField.innerText = improvedPrompt;
      // } else {
      //   (inputField as HTMLTextAreaElement | HTMLInputElement).value = improvedPrompt;
      // }

      // Show modal with optimized prompts
      // showOptimizedPromptsModal(optimizedPrompts, inputField);
      return optimizedPrompts;

    } catch (error) {
      console.error('Error in perfect button handler:', error);
      alert('Error: Could not optimize the prompt. Please try again.');
    } finally {
      button.classList.remove('loading');
    }
  });
} 